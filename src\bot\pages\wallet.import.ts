import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "../baseHandler"
import { ChainSelectionKeyboard } from "../keyboards/chainSelection"
import { User, Wallet } from "../../models"
import { BlockchainWallet } from "../../blockchain/wallet"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Wallet Import Page
 * Handles wallet import flow and chain selection
 */
export class WalletImportPage extends BaseHandler {
  /**
   * Show import wallet form with chain selection
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = ChainSelectionKeyboard.create("import_chain")
    await this.updateKeyboard(ctx, "importwallet_usage", keyboard)
  }

  /**
   * Show wallet name prompt for selected chain
   * @param ctx Callback query context
   * @param chain Selected blockchain chain
   * @param userId User's Telegram ID
   */
  async showNamePrompt(ctx: any, chain: string, userId: number): Promise<void> {
    // Set up session to wait for wallet name input with selected chain
    await this.sessionSet(userId, {
      state: "waiting_import_details",
      operation: "import_wallet",
      chain: chain,
      step: "wallet_name"
    })

    const { symbol, chainDisplayName } = BlockchainConfig.get[chain]
    const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")

    await this.updateKeyboard(ctx, "importwallet_name_prompt", keyboard, {
      chainName: chainDisplayName,
      symbol
    })
  }

  /**
   * Show private key prompt
   * @param ctx Message context
   * @param chain Blockchain chain
   * @param walletName Wallet name
   * @param userId User's Telegram ID
   */
  async showPrivateKeyPrompt(ctx: any, chain: string, walletName: string, _userId: number): Promise<void> {
    const { symbol, chainDisplayName } = BlockchainConfig.get[chain]
    const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")

    await this.replyWithKeyboard(ctx, "importwallet_private_key_prompt", keyboard, {
      chainName: chainDisplayName,
      symbol,
      walletName: walletName
    })
  }

  /**
   * Show wallet import success page
   * @param ctx Message context
   * @param wallet Imported wallet object
   * @param chain Blockchain chain
   */
  async showSuccess(ctx: any, wallet: any, chain: string): Promise<void> {
    const successKeyboard = new InlineKeyboard().text("👁️ View Wallet", `wallet_action:view:${wallet.id}`).text("📥 Import Another", "wallet_action:import").row().text("🏠 Back to Main Menu", "main_menu")

    const { chainDisplayName } = BlockchainConfig.get[chain]
    await this.replyWithKeyboard(ctx, "importwallet_success", successKeyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }

  /**
   * Show wallet import failure page
   * @param ctx Message context
   * @param chain Blockchain chain
   * @param errorType Type of error (duplicate_name, duplicate_address, invalid_key, general)
   * @param address Optional address for duplicate address error
   */
  async showFailure(ctx: any, chain: string, errorType: string, address?: string): Promise<void> {
    let keyboard: InlineKeyboard
    let contentKey: string

    switch (errorType) {
      case "duplicate_name":
        keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        contentKey = "wallet_name_exists"
        break

      case "duplicate_address":
        keyboard = new InlineKeyboard().text("💼 View My Wallets", "wallet_action:list").text("📥 Import Different Wallet", "wallet_action:import").row().text("🏠 Back to Main Menu", "main_menu")
        const { chainDisplayName } = BlockchainConfig.get[chain]
        await this.replyWithKeyboard(ctx, "importwallet_duplicate_address", keyboard, {
          chainName: chainDisplayName,
          address: address
        })
        return

      case "invalid_key":
        keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        contentKey = "private_key_invalid"
        break

      default:
        keyboard = new InlineKeyboard().text("🔄 Try Again", "wallet_action:import").text("🏠 Back to Main Menu", "main_menu")
        contentKey = "importwallet_failed"
    }

    await this.replyWithKeyboard(ctx, contentKey, keyboard)
  }

  /**
   * Process wallet import with provided details
   * @param ctx Message context
   * @param userId User's Telegram ID
   * @param walletName Wallet name
   * @param privateKey Private key
   * @param chain Blockchain chain
   */
  async processImport(ctx: any, userId: number, walletName: string, privateKey: string, chain: string): Promise<void> {
    try {
      const user = await User.getById(userId)
      if (!user) {
        await this.sessionDelete(userId)
        const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "user_not_found", keyboard)
        return
      }

      // Derive address from private key
      const address = await new BlockchainWallet(chain).getAddress(privateKey)

      if (!address) {
        await this.showFailure(ctx, chain, "invalid_key")
        return
      }

      // Get chain ID from blockchain config
      const { chainId } = BlockchainConfig.get[chain]

      const wallet = await Wallet.import(user.id, walletName, chain, chainId, address, privateKey, this.passwordWallet)

      await this.sessionDelete(userId)

      if (wallet && typeof wallet === "object") {
        await this.showSuccess(ctx, wallet, chain)
      } else if (wallet === "DUPLICATE_NAME") {
        await this.showFailure(ctx, chain, "duplicate_name")
      } else if (wallet === "DUPLICATE_ADDRESS") {
        await this.showFailure(ctx, chain, "duplicate_address", address)
      } else {
        await this.showFailure(ctx, chain, "general")
      }
    } catch (error) {
      log.error(`Error WalletImportPage.processImport: ${error}`)
      await this.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "importwallet_error", errorKeyboard)
    }
  }
}
