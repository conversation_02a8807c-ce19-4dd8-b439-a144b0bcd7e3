import { BaseHandler } from "../baseHandler"
import { MainMenuKeyboard } from "../keyboards/mainMenu"

/**
 * Main Menu Page
 * Handles the main menu display and navigation
 */
export class MainMenuPage extends BaseHandler {
  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = MainMenuKeyboard.create()
    await this.updateKeyboard(ctx, "start", keyboard)
  }
}
