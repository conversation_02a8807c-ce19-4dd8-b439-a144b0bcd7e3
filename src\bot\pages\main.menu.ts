import { InlineKeyboard } from "grammy"
import { <PERSON>Hand<PERSON> } from "../baseHandler"

/**
 * Main Menu Page
 * Handles the main menu display and navigation
 */
export class MainMenuPage extends BaseHandler {
  /**
   * Create main menu keyboard
   */
  private static createKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("💼 My Wallets", "wallet_action:list").text("📊 View Stats", "wallet_action:stats").row().text("⚙️ Settings", "wallet_action:settings").text("❓ Help", "wallet_action:help")
  }

  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = MainMenuPage.createKeyboard()
    await this.updateKeyboard(ctx, "start", keyboard)
  }
}
