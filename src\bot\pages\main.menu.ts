import { BaseHandler } from "../baseHandler"
import { KeyboardHandler } from "../handlers/keyboardHandler"

/**
 * Main Menu Page
 * Handles the main menu display and navigation
 */
export class MainMenuPage extends BaseHandler {
  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = KeyboardHandler.createMainMenuKeyboard()
    await this.updateKeyboard(ctx, "start", keyboard)
  }
}
