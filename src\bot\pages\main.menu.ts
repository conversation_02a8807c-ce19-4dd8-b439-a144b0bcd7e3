import { InlineKeyboard } from "grammy"
import { BaseHandler } from "../baseHandler"

/**
 * Main Menu Page
 * Handles the main menu display and navigation
 */
export class MainMenuPage extends BaseHandler {
  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()
      .text("💼 My Wallets", "wallet_action:list")
      .text("📊 View Stats", "wallet_action:stats")
      .row()
      .text("⚙️ Settings", "wallet_action:settings")
      .text("❓ Help", "wallet_action:help")

    await this.updateKeyboard(ctx, "start", keyboard)
  }
}
