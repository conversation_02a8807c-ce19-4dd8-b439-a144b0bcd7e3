import { InlineKeyboard } from "grammy"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Chain Selection Keyboard
 * Creates keyboards for blockchain chain selection
 */
export class ChainSelectionKeyboard {
  /**
   * Create chain selection keyboard
   * @param callbackPrefix Callback prefix for chain selection
   * @param maxChains Maximum number of chains to show
   */
  static create(callbackPrefix: string, maxChains: number = 6): InlineKeyboard {
    const keyboard = new InlineKeyboard()
    ChainSelectionKeyboard.addChainButtons(keyboard, callbackPrefix, maxChains)
    keyboard.row().text("🔙 Back to Menu", "back:main_menu")
    return keyboard
  }

  /**
   * Add chain selection buttons to keyboard dynamically
   * @param keyboard InlineKeyboard instance to add buttons to
   * @param callbackPrefix Prefix for callback data (e.g., "create_wallet" or "import_chain")
   * @param maxChains Maximum number of chains to show (default: 6)
   */
  static addChainButtons(keyboard: InlineKeyboard, callbackPrefix: string, maxChains: number = 6): void {
    const chains = BlockchainConfig.listName.slice(0, maxChains)
    for (let i = 0; i < chains.length; i += 2) {
      keyboard.row()
      const chain1 = chains[i]
      const chain2 = chains[i + 1]

      // Add first chain button
      if (chain1 && BlockchainConfig.get[chain1]) {
        const { chainDisplayName } = BlockchainConfig.get[chain1]
        keyboard.text(`🔗 ${chainDisplayName}`, `${callbackPrefix}:${chain1}`)
      }

      // Add second chain button if exists
      if (chain2 && BlockchainConfig.get[chain2]) {
        const { chainDisplayName } = BlockchainConfig.get[chain2]
        keyboard.text(`🔗 ${chainDisplayName}`, `${callbackPrefix}:${chain2}`)
      }
    }
  }

  /**
   * Create chain info keyboard
   * @param chain Chain name
   */
  static createChainInfo(chain: string): InlineKeyboard {
    return new InlineKeyboard()
      .text("💰 View Fees", `chain_detail:fees:${chain}`)
      .text("📊 Network Stats", `chain_detail:stats:${chain}`)
      .row()
      .text("🔙 Back to Menu", "back:main_menu")
  }
}
