import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON>and<PERSON> } from "../baseHandler"
import { TradingHistory } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Wallet Statistics Page
 * Handles wallet statistics display
 */
export class WalletStatsPage extends BaseHandler {
  /**
   * Create back to wallet keyboard
   * @param walletId Wallet ID
   */
  private static createBackToWalletKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }
  /**
   * Show wallet statistics
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  async show(ctx: any, wallet: any): Promise<void> {
    try {
      const stats = await TradingHistory.getWalletStats(BigInt(wallet.id))

      const keyboard = WalletStatsPage.createBackToWalletKeyboard(wallet.id)

      if (!stats) {
        await this.updateKeyboard(ctx, "stats_empty", keyboard, { walletName: wallet.name })
        return
      }

      const successRate = stats.totalTrades > 0 ? ((stats.successTrades / stats.totalTrades) * 100).toFixed(2) : "0"
      const { symbol } = BlockchainConfig.get[wallet.chain]

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        walletName: wallet.name,
        totalTrades: stats.totalTrades,
        successTrades: stats.successTrades,
        failedTrades: stats.failedTrades,
        successRate,
        totalVolume: stats.totalVolume.toString(),
        successVolume: stats.successVolume.toString(),
        symbol,
        buyTrades: stats.buyTrades,
        sellTrades: stats.sellTrades
      })
    } catch (error) {
      log.error(`Error WalletStatsPage.show: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }
}
