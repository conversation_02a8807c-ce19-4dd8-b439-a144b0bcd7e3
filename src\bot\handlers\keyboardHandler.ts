import { InlineKeyboard } from "grammy"
import { BaseHandler } from "../baseHandler"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Keyboard Handler
 * Manages all inline keyboard creation, updates, and utilities
 * Provides centralized keyboard management for consistent UI
 */
export class KeyboardHandler extends BaseHandler {
  /**
   * Create main menu keyboard
   */
  static createMainMenuKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("💼 My Wallets", "wallet_action:list").text("📊 View Stats", "wallet_action:stats").row().text("⚙️ Settings", "wallet_action:settings").text("❓ Help", "wallet_action:help")
  }

  /**
   * Create wallet list keyboard
   * @param wallets Array of wallet objects
   * @param currentPage Current page number (0-based)
   * @param totalPages Total number of pages
   */
  static createWalletListKeyboard(wallets: any[], currentPage: number, totalPages: number): InlineKeyboard {
    const keyboard = new InlineKeyboard()

    // Add wallet buttons
    wallets.forEach((wallet) => {
      keyboard.text(`💼 ${wallet.name}`, `wallet_select:${wallet.id}`).row()
    })

    // Add pagination if needed
    if (totalPages > 1) {
      keyboard.row()
      if (currentPage > 0) {
        keyboard.text("⬅️ Previous", `page:wallets:${currentPage - 1}`)
      }
      keyboard.text(`${currentPage + 1}/${totalPages}`, "noop")
      if (currentPage < totalPages - 1) {
        keyboard.text("➡️ Next", `page:wallets:${currentPage + 1}`)
      }
    }

    // Add action buttons
    keyboard.row().text("➕ Create", "wallet_action:create").text("📥 Import", "wallet_action:import").text("🔙 Menu", "back:main_menu")

    return keyboard
  }

  /**
   * Create empty wallet state keyboard
   */
  static createEmptyWalletKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("➕ Create Wallet", "wallet_action:create").text("📥 Import Wallet", "wallet_action:import").row().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create wallet details keyboard
   * @param walletId Wallet ID
   */
  static createWalletDetailsKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard()
      .text("💰 Balance", `wallet_detail:balance:${walletId}`)
      .text("📊 History", `wallet_detail:history:${walletId}`)
      .row()
      .text("📈 Stats", `wallet_detail:stats:${walletId}`)
      .text("🔑 Export Key", `wallet_detail:export:${walletId}`)
      .row()
      .text("🗑️ Delete", `wallet_detail:delete:${walletId}`)
      .text("🔙 Back to Wallets", "wallet_action:list")
  }

  /**
   * Create back to wallet keyboard
   * @param walletId Wallet ID
   */
  static createBackToWalletKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }

  /**
   * Create chain selection keyboard
   * @param callbackPrefix Callback prefix for chain selection
   * @param maxChains Maximum number of chains to show
   */
  static createChainSelectionKeyboard(callbackPrefix: string, maxChains: number = 6): InlineKeyboard {
    const keyboard = new InlineKeyboard()
    KeyboardHandler.addChainButtons(keyboard, callbackPrefix, maxChains)
    keyboard.row().text("🔙 Back to Menu", "back:main_menu")
    return keyboard
  }

  /**
   * Add chain selection buttons to keyboard dynamically
   * @param keyboard InlineKeyboard instance to add buttons to
   * @param callbackPrefix Prefix for callback data (e.g., "create_wallet" or "import_chain")
   * @param maxChains Maximum number of chains to show (default: 6)
   */
  static addChainButtons(keyboard: InlineKeyboard, callbackPrefix: string, maxChains: number = 6): void {
    const chains = BlockchainConfig.listName.slice(0, maxChains)
    for (let i = 0; i < chains.length; i += 2) {
      keyboard.row()
      const chain1 = chains[i]
      const chain2 = chains[i + 1]

      // Add first chain button
      if (chain1 && BlockchainConfig.get[chain1]) {
        const { chainDisplayName } = BlockchainConfig.get[chain1]
        keyboard.text(`🔗 ${chainDisplayName}`, `${callbackPrefix}:${chain1}`)
      }

      // Add second chain button if exists
      if (chain2 && BlockchainConfig.get[chain2]) {
        const { chainDisplayName } = BlockchainConfig.get[chain2]
        keyboard.text(`🔗 ${chainDisplayName}`, `${callbackPrefix}:${chain2}`)
      }
    }
  }

  /**
   * Create confirmation keyboard
   * @param confirmAction Confirm action callback
   * @param cancelAction Cancel action callback
   * @param backAction Optional back action callback
   */
  static createConfirmationKeyboard(confirmAction: string, cancelAction: string, backAction?: string): InlineKeyboard {
    const keyboard = new InlineKeyboard().text("✅ Yes", confirmAction).text("❌ No", cancelAction)

    if (backAction) {
      keyboard.row().text("🔙 Back", backAction)
    }

    return keyboard
  }

  /**
   * Create export confirmation keyboard
   * @param walletId Wallet ID
   */
  static createExportConfirmationKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔑 Yes, Export Key", `export_confirm:${walletId}`).text("🔙 Cancel", `wallet_select:${walletId}`)
  }

  /**
   * Create delete confirmation keyboard
   * @param walletId Wallet ID
   */
  static createDeleteConfirmationKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("✅ Yes, Delete", `confirm_delete:${walletId}`).text("❌ Cancel", "cancel").row().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }

  /**
   * Create input prompt keyboard
   * @param backAction Back action callback
   * @param cancelAction Cancel action callback
   */
  static createInputPromptKeyboard(backAction: string, cancelAction: string = "cancel"): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back", backAction).text("❌ Cancel", cancelAction)
  }

  /**
   * Create success keyboard
   * @param primaryAction Primary action callback and text
   * @param secondaryAction Secondary action callback and text
   * @param backToMenu Whether to include back to menu button
   */
  static createSuccessKeyboard(primaryAction: { text: string; callback: string }, secondaryAction: { text: string; callback: string }, backToMenu: boolean = true): InlineKeyboard {
    const keyboard = new InlineKeyboard().text(primaryAction.text, primaryAction.callback).text(secondaryAction.text, secondaryAction.callback)

    if (backToMenu) {
      keyboard.row().text("🏠 Back to Main Menu", "main_menu")
    }

    return keyboard
  }

  /**
   * Create failure keyboard
   * @param retryAction Retry action callback
   * @param backToMenu Whether to include back to menu button
   */
  static createFailureKeyboard(retryAction: string, backToMenu: boolean = true): InlineKeyboard {
    const keyboard = new InlineKeyboard().text("🔄 Try Again", retryAction)

    if (backToMenu) {
      keyboard.text("🏠 Back to Main Menu", "main_menu")
    }

    return keyboard
  }

  /**
   * Create settings keyboard
   */
  static createSettingsKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🌐 Language", "settings:language").text("🔔 Notifications", "settings:notifications").row().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create help keyboard
   */
  static createHelpKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("💼 Wallet Commands", "help:wallets").text("📊 Trading Commands", "help:trading").row().text("⚙️ Config Commands", "help:config").text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create back to settings keyboard
   */
  static createBackToSettingsKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Settings", "wallet_action:settings").text("🏠 Main Menu", "back:main_menu")
  }

  /**
   * Create back to help keyboard
   */
  static createBackToHelpKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Help", "wallet_action:help").text("🏠 Main Menu", "back:main_menu")
  }

  /**
   * Create chain info keyboard
   * @param chain Chain name
   */
  static createChainInfoKeyboard(chain: string): InlineKeyboard {
    return new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("📊 Network Stats", `chain_detail:stats:${chain}`).row().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create account stats keyboard
   */
  static createAccountStatsKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Update keyboard with error handling
   * @param ctx Context object
   * @param contentKey Content key
   * @param keyboard New keyboard
   * @param data Optional data for content replacement
   */
  async updateKeyboardSafe(ctx: any, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>): Promise<void> {
    try {
      await this.updateKeyboard(ctx, contentKey, keyboard, data)
    } catch (error) {
      log.error(`Error updating keyboard: ${error}`)
      await this.replyWithKeyboard(ctx, contentKey, keyboard, data)
    }
  }

  /**
   * Reply with keyboard and error handling
   * @param ctx Context object
   * @param contentKey Content key
   * @param keyboard Keyboard
   * @param data Optional data for content replacement
   */
  async replyWithKeyboardSafe(ctx: any, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>): Promise<void> {
    try {
      await this.replyWithKeyboard(ctx, contentKey, keyboard, data)
    } catch (error) {
      log.error(`Error replying with keyboard: ${error}`)
      await this.reply(ctx, contentKey, data)
    }
  }
}
