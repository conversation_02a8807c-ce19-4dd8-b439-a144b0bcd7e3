import { <PERSON>Hand<PERSON> } from "../baseHandler"
import { WalletDetailsKeyboard } from "../keyboards/walletDetails"
import { Wallet } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Wallet Details Page
 * Handles individual wallet details display and actions
 */
export class WalletDetailsPage extends BaseHandler {
  /**
   * Show wallet details with action buttons
   * @param ctx Callback query context
   * @param walletId Wallet ID
   * @param telegramId User's Telegram ID for ownership validation
   */
  async show(ctx: any, walletId: number, telegramId: number): Promise<void> {
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    // Show wallet details with action buttons
    const keyboard = WalletDetailsKeyboard.create(walletId)

    const { symbol, chainDisplayName } = BlockchainConfig.get[wallet.chain]
    const balance = wallet.balance.toString()
    const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

    await this.updateKeyboard(ctx, "wallet_info", keyboard, {
      name: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address,
      balance,
      symbol,
      chainId: wallet.chainId,
      createdDate,
      createdBy: wallet.createdBy
    })
  }
}
