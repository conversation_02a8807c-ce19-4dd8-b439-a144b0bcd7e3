import { InlineKeyboard } from "grammy"
import { Base<PERSON>andler } from "../baseHandler"
import { Wallet } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Wallet Details Page
 * Handles individual wallet details display and actions
 */
export class WalletDetailsPage extends BaseHandler {
  /**
   * Show wallet details with action buttons
   * @param ctx Callback query context
   * @param walletId Wallet ID
   * @param telegramId User's Telegram ID for ownership validation
   */
  async show(ctx: any, walletId: number, telegramId: number): Promise<void> {
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    // Show wallet details with action buttons
    const keyboard = new InlineKeyboard()
      .text("💰 Balance", `wallet_detail:balance:${walletId}`)
      .text("📊 History", `wallet_detail:history:${walletId}`)
      .row()
      .text("📈 Stats", `wallet_detail:stats:${walletId}`)
      .text("🔑 Export Key", `wallet_detail:export:${walletId}`)
      .row()
      .text("🗑️ Delete", `wallet_detail:delete:${walletId}`)
      .text("🔙 Back to Wallets", "wallet_action:list")

    const { symbol, chainDisplayName } = BlockchainConfig.get[wallet.chain]
    const balance = wallet.balance.toString()
    const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

    await this.updateKeyboard(ctx, "wallet_info", keyboard, {
      name: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address,
      balance,
      symbol,
      chainId: wallet.chainId,
      createdDate,
      createdBy: wallet.createdBy
    })
  }
}
