/**
 * Pages module index file
 *
 * This file exports all page classes for the optimized page-based architecture.
 * Each page handles a specific UI screen/page in the bot with direct access for efficiency.
 */

// Export individual page classes for direct access
export { MainMenuPage } from "./main.menu"
export { WalletListPage } from "./wallet.list"
export { WalletDetailsPage } from "./wallet.details"
export { WalletBalancePage } from "./wallet.balance"
export { WalletHistoryPage } from "./wallet.history"
export { WalletStatsPage } from "./wallet.stats"
export { WalletCreatePage } from "./wallet.create"
export { WalletImportPage } from "./wallet.import"
export { WalletExportPage } from "./wallet.export"
export { WalletDeletePage } from "./wallet.delete"
export { AccountStatsPage } from "./account.stats"
export { SettingsMainPage } from "./settings.main"
export { HelpSectionsPage } from "./help.sections"
export { ChainInfoPage } from "./chain.info"
