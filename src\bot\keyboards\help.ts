import { InlineKeyboard } from "grammy"

/**
 * Help Keyboard
 * Creates keyboards for help and documentation screens
 */
export class HelpKeyboard {
  /**
   * Create help keyboard
   */
  static create(): InlineKeyboard {
    return new InlineKeyboard()
      .text("💼 Wallet Commands", "help:wallets")
      .text("📊 Trading Commands", "help:trading")
      .row()
      .text("⚙️ Config Commands", "help:config")
      .text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create back to help keyboard
   */
  static createBackToHelp(): InlineKeyboard {
    return new InlineKeyboard()
      .text("🔙 Back to Help", "wallet_action:help")
      .text("🏠 Main Menu", "back:main_menu")
  }
}
