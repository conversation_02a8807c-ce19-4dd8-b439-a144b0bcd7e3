/**
 * User schema definition
 *
 * This file defines the users table schema.
 * The primary key is the Telegram user ID.
 * This table stores user information and preferences, while wallet-specific
 * data is stored in the wallets table.
 */

import { sql } from "drizzle-orm"
import { boolean, index, integer, pgTable, timestamp, varchar } from "drizzle-orm/pg-core"

export const tableUsers = pgTable(
  "users",
  {
    id: integer().primaryKey(), // Telegram user ID as primary key
    username: varchar({ length: 32 }).notNull().default(``), // Telegram username
    fullname: varchar({ length: 255 }).notNull().default(``), // Telegram user fullname
    isActive: boolean().notNull().default(true), // User status
    fee: varchar({ length: 10 }).notNull().default("0"), // Fee percentage for trading (e.g., "0.3" for 0.3%)
    language: varchar({ length: 10 }).notNull().default("en"), // User's preferred language
    updatedAt: timestamp()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => new Date()),
    createdAt: timestamp().default(sql`CURRENT_TIMESTAMP`)
  },
  (table) => [
    // Add index on username for faster searches
    index("user_username_idx").on(table.username),
    // Add index on isActive for faster filtering
    index("user_is_active_idx").on(table.isActive)
  ]
)
