import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "./baseHandler"
import { User, Wallet } from "../models"
import { data<PERSON>hainName, type <PERSON><PERSON>hain<PERSON>ame } from "../data"
import { BlockchainWallet } from "../blockchain/wallet"
import { BlockchainConfig } from "../blockchain/config"
import { log } from "../utils/log"

/**
 * Wallet creation module
 * Handles wallet creation functionality including chain selection, name input processing, and wallet generation
 */
export class WalletCreator extends BaseHandler {
  /**
   * Add chain selection buttons to keyboard dynamically
   * @param keyboard InlineKeyboard instance to add buttons to
   * @param callbackPrefix Prefix for callback data (e.g., "create_wallet" or "import_chain")
   * @param maxChains Maximum number of chains to show (default: 6)
   */
  private addChainButtons(keyboard: InlineKeyboard, callbackPrefix: string, maxChains: number = 6): void {
    const chains = dataChainName.slice(0, maxChains) as TypeChainName[]
    for (let i = 0; i < chains.length; i += 2) {
      keyboard.row()
      const chain1 = chains[i]
      const chain2 = chains[i + 1]

      // Add first chain button
      if (chain1 && BlockchainConfig[chain1]) {
        const config1 = BlockchainConfig[chain1]
        keyboard.text(`🔗 ${config1.name}`, `${callbackPrefix}:${chain1}`)
      }

      // Add second chain button if exists
      if (chain2 && BlockchainConfig[chain2]) {
        const config2 = BlockchainConfig[chain2]
        keyboard.text(`🔗 ${config2.name}`, `${callbackPrefix}:${chain2}`)
      }
    }
  }

  /**
   * Show create wallet form
   * @param ctx Callback query context
   */
  async showCreateWalletForm(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()

    // Add chain selection buttons dynamically
    this.addChainButtons(keyboard, "create_wallet")
    keyboard.row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "createwallet_usage", keyboard)
  }

  /**
   * Handle create wallet chain selection - directly prompt for wallet name
   * @param ctx Callback query context
   * @param params Chain parameters
   */
  async handleCreateWalletChain(ctx: any, params: string[]): Promise<void> {
    const chain = params[0] as TypeChainName
    const userId = ctx.from?.id as number

    // Set up session to wait for wallet name input directly
    await this.sessionSet(userId, {
      state: "waiting_wallet_name",
      chain: chain,
      operation: "create_wallet"
    })

    const keyboard = new InlineKeyboard()
      .text("🔙 Back to Chains", "wallet_action:create")
      .text("❌ Cancel", "cancel")

    const { symbol, name } = BlockchainConfig[chain]
    await this.updateKeyboard(ctx, "createwallet_name_prompt", keyboard, {
      chainName: name,
      symbol
    })
  }

  /**
   * Process wallet name input from user
   * @param ctx Message context
   * @param userId Telegram user ID
   * @param walletName User input for wallet name
   * @param session Current session data
   */
  async processWalletNameInput(ctx: any, userId: number, walletName: string, session: Record<string, any>): Promise<void> {
    try {
      // Validate wallet name
      if (walletName.length > 32) {
        const keyboard = new InlineKeyboard()
          .text("🔙 Back to Chains", "wallet_action:create")
          .text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_too_long", keyboard)
        return
      }

      if (!/^[a-zA-Z0-9_-]+$/.test(walletName)) {
        const keyboard = new InlineKeyboard()
          .text("🔙 Back to Chains", "wallet_action:create")
          .text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_invalid_chars", keyboard)
        return
      }

      // Check if wallet name already exists for this user
      const user = await User.getById(userId)
      if (!user) {
        await this.sessionDelete(userId)
        const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "user_not_found", keyboard)
        return
      }

      const existingWallet = await Wallet.getByName(user.id, walletName)
      if (existingWallet) {
        const keyboard = new InlineKeyboard()
          .text("🔙 Back to Chains", "wallet_action:create")
          .text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
        return
      }

      // Generate wallet address and private key
      const chain = session.chain as TypeChainName
      const { address, privateKey } = new BlockchainWallet(chain).generate()

      // Get chain ID from blockchain config
      const { chainId } = BlockchainConfig[chain]

      // Create the wallet with encryption
      const wallet = await Wallet.create(user.id, walletName, chain, chainId, address, privateKey, "system", this.passwordWallet)

      if (wallet && typeof wallet === "object") {
        // Success - wallet created
        await this.sessionDelete(userId)

        const successKeyboard = new InlineKeyboard()
          .text("👁️ View Wallet", `wallet_action:view:${wallet.id}`)
          .text("➕ Create Another", `create_wallet:${chain}`)
          .row()
          .text("🏠 Back to Main Menu", "main_menu")
        const { name: chainDisplayName } = BlockchainConfig[chain]
        await this.replyWithKeyboard(ctx, "createwallet_success", successKeyboard, {
          walletName: wallet.name,
          chainName: chainDisplayName,
          address: wallet.address
        })
      } else if (wallet === "DUPLICATE_NAME") {
        // Wallet name already exists
        const keyboard = new InlineKeyboard()
          .text("🔙 Back to Chains", "wallet_action:create")
          .text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
      } else if (wallet === "DUPLICATE_ADDRESS") {
        // This is extremely unlikely for generated wallets, but handle it for completeness
        const keyboard = new InlineKeyboard()
          .text("🔄 Try Again", `create_wallet:${chain}`)
          .text("🏠 Back to Main Menu", "main_menu")
        const { name: chainDisplayName } = BlockchainConfig[chain]
        await this.replyWithKeyboard(ctx, "importwallet_duplicate_address", keyboard, {
          chainName: chainDisplayName,
          address: address
        })
      } else {
        // General creation failure
        await this.sessionDelete(userId)

        const failureKeyboard = new InlineKeyboard()
          .text("🔄 Try Again", `create_wallet:${chain}`)
          .text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "createwallet_failed", failureKeyboard)
      }
    } catch (error) {
      log.error(`Error processWalletNameInput: ${error}`)
      await this.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")

      await this.replyWithKeyboard(ctx, "createwallet_error", errorKeyboard)
    }
  }
}
