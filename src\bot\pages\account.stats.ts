import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON>and<PERSON> } from "../baseHandler"
import { Wallet, TradingHistory } from "../../models"
import { dataChainName } from "../../data"
import { log } from "../../utils/log"

/**
 * Account Statistics Page
 * Handles comprehensive trading analytics and statistics display
 */
export class AccountStatsPage extends BaseHandler {
  /**
   * Show comprehensive trading analytics
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const wallets = await Wallet.getAllForOwner(telegramId)
      const totalWallets = wallets.length

      // Calculate comprehensive trading statistics across all wallets
      let totalTrades = 0
      let totalSuccessful = 0
      let totalFailed = 0
      let totalVolume = BigInt(0)
      let totalSuccessVolume = BigInt(0)
      let totalBuyTrades = 0
      let totalSellTrades = 0

      // Get statistics for each chain
      const chainStatsArray: any[] = []

      for (const chain of dataChainName) {
        const stats = await TradingHistory.getChainStats(chain)
        if (stats && stats.totalTrades > 0) {
          chainStatsArray.push({
            chainName: chain,
            ...stats,
            successRate: ((stats.successTrades / stats.totalTrades) * 100).toFixed(2)
          })

          // Add to overall totals
          totalTrades += stats.totalTrades
          totalSuccessful += stats.successTrades
          totalFailed += stats.failedTrades
          totalVolume += stats.totalVolume
          totalSuccessVolume += stats.successVolume
          totalBuyTrades += stats.buyTrades
          totalSellTrades += stats.sellTrades
        }
      }

      const overallSuccessRate = totalTrades > 0 ? ((totalSuccessful / totalTrades) * 100).toFixed(2) : "0"
      const activeChains = chainStatsArray.length

      // Create network performance summary string
      const networkPerformance =
        chainStatsArray.length > 0
          ? chainStatsArray
              .map(({ chainName, totalTrades, successRate, uniqueWallets }) => 
                `• <b>${chainName}:</b> ${totalTrades} trades | ${successRate}% success | ${uniqueWallets} wallets`)
              .join("\n")
          : "• No trading activity found across networks"

      // Create buy/sell ratio string
      const buyToSellRatio = totalSellTrades > 0 ? `${totalBuyTrades}:${totalSellTrades}` : totalBuyTrades > 0 ? "All Buy Orders" : "No Trades"

      const keyboard = new InlineKeyboard().text("🔙 Back to Menu", "back:main_menu")

      // Create portfolio status message
      const portfolioStatus =
        totalWallets > 0
          ? `• Managing ${totalWallets} wallet(s) across ${activeChains} network(s)\n• Real-time balance tracking and monitoring\n• Multi-chain portfolio diversification`
          : "• No wallets found - create your first wallet to start trading\n• Import existing wallets to manage your portfolio\n• Start building your multi-chain trading strategy"

      // Create trading insights
      const successRateNum = parseFloat(overallSuccessRate)
      const tradingInsights =
        totalTrades > 0
          ? `• Success rate of ${overallSuccessRate}% indicates ${successRateNum >= 70 ? "strong" : successRateNum >= 50 ? "moderate" : "developing"} trading performance\n• ${
              totalBuyTrades > totalSellTrades ? "Buy-focused" : totalSellTrades > totalBuyTrades ? "Sell-focused" : "Balanced"
            } trading strategy detected\n• Active across ${activeChains} blockchain network(s)\n• Total trading volume: ${totalVolume.toString()}`
          : "• No trading activity detected yet\n• Start executing trades to see performance analytics\n• Monitor success rates and optimize your strategy\n• Track volume and profitability across networks"

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        totalWallets,
        totalTrades,
        totalSuccessful,
        totalFailed,
        overallSuccessRate,
        totalVolume: totalVolume.toString(),
        totalSuccessVolume: totalSuccessVolume.toString(),
        totalBuyTrades,
        totalSellTrades,
        activeChains,
        networkPerformance,
        buyToSellRatio,
        portfolioStatus,
        tradingInsights,
        username: ctx.from?.username || "Unknown"
      })
    } catch (error) {
      log.error(`Error AccountStatsPage.show: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }
}
