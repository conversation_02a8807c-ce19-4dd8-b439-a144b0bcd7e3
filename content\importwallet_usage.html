📥 <b>Import Existing Wallet</b>

Import your existing crypto wallet by selecting the blockchain network below.

<b>🔗 Supported Networks:</b>
• <b>Solana Mainnet</b> - Production Solana network
• <b>Solana Devnet</b> - Solana testing network
• <b>Ethereum Mainnet</b> - Production Ethereum network
• <b>Ethereum Testnet</b> - Ethereum testing network

<b>📋 Import Process:</b>
1. <b>Select Network</b> - Choose your wallet's blockchain network
2. <b>Enter Name</b> - Provide a unique name for the wallet
3. <b>Enter Private Key</b> - Securely input your wallet's private key

<b>📝 Requirements:</b>
• You must own and control the wallet being imported
• Valid private key for the selected network
• Unique wallet name (32 characters max)
• Private key will be encrypted and stored securely

<b>⚠️ Security Warning:</b>
Only import wallets that you own and control. Never share your private keys with anyone! Your private key will be securely encrypted before storage.

<b>Select your blockchain network to begin:</b>