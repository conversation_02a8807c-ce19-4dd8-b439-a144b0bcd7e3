import { <PERSON>Handler } from "../baseHandler"
import { WalletDetailsKeyboard } from "../keyboards/walletDetails"
import { TradingHistory } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Wallet History Page
 * Handles wallet trading history display
 */
export class WalletHistoryPage extends BaseHandler {
  /**
   * Show wallet trading history
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  async show(ctx: any, wallet: any): Promise<void> {
    try {
      const history = await TradingHistory.getForWallet(BigInt(wallet.id), 10)

      const keyboard = WalletDetailsKeyboard.createBackToWallet(wallet.id)

      if (history.length === 0) {
        await this.updateKeyboard(ctx, "history_empty", keyboard, { walletName: wallet.name })
        return
      }

      const historyText = history
        .map((trade, index) => {
          const operation = trade.operation === "B" ? "🟢 BUY" : "🔴 SELL"
          const status = trade.success ? "✅" : "❌"
          const amount = trade.amount.toString()
          const date = trade.createdAt?.toLocaleDateString() || "Unknown"
          const { symbol } = BlockchainConfig.get[trade.chain]

          return `${index + 1}. ${operation} ${status}\n   💰 ${amount} ${symbol}\n   📅 ${date}`
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "history_display", keyboard, {
        walletName: wallet.name,
        historyText
      })
    } catch (error) {
      log.error(`Error WalletHistoryPage.show: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }
}
