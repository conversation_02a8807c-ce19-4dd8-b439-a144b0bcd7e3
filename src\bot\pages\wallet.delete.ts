import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON>and<PERSON> } from "../baseHandler"
import { User, Wallet } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Wallet Delete Page
 * Handles wallet deletion confirmation and processing
 */
export class WalletDeletePage extends BaseHandler {
  /**
   * Create delete confirmation keyboard
   * @param walletId Wallet ID
   */
  private static createDeleteConfirmationKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("✅ Yes, Delete", `confirm_delete:${walletId}`).text("❌ Cancel", "cancel").row().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }
  /**
   * Show delete confirmation dialog
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  async showConfirmation(ctx: any, wallet: any): Promise<void> {
    const keyboard = WalletDeletePage.createDeleteConfirmationKeyboard(wallet.id)

    const { chainDisplayName } = BlockchainConfig.get[wallet.chain]
    await this.updateKeyboard(ctx, "deletewallet_confirm", keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }

  /**
   * Process wallet deletion
   * @param ctx Callback query context
   * @param walletId Wallet ID
   * @param telegramId User's Telegram ID for ownership validation
   */
  async processDelete(ctx: any, walletId: number, telegramId: number): Promise<void> {
    const user = await User.getById(telegramId)

    if (!user) {
      await this.answerCallback(ctx, "callback_user_not_found")
      await this.deleteKeyboard(ctx)
      return
    }

    // Use secure method to get wallet with ownership validation
    const wallet = await Wallet.getByIdForOwner(walletId, user.id)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    const success = await Wallet.remove(walletId, user.id)

    if (success) {
      await this.answerCallback(ctx, "callback_wallet_deleted_success")
      // Navigate back to wallet list
      const { WalletListPage } = await import("./wallet.list")
      const walletListPage = new WalletListPage()
      await walletListPage.show(ctx)
    } else {
      await this.answerCallback(ctx, "callback_wallet_delete_failed")
      await this.deleteKeyboard(ctx)
    }
  }
}
