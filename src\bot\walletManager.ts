import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "./baseHandler"
import { User, Wallet, TradingHistory } from "../models"
import { data<PERSON>hainName, type Type<PERSON>hainName } from "../data"
import { BlockchainConfig } from "../blockchain/config"
import { log } from "../utils/log"

/**
 * Wallet management module
 * Handles wallet listing, selection, details, balance, history, and statistics
 */
export class WalletManager extends BaseHandler {
  /**
   * Handle wallet action callbacks
   * @param ctx Callback query context
   * @param params Action parameters
   */
  async handleWalletAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "list":
        await this.showWalletList(ctx)
        break
      case "view":
        await this.handleWalletSelect(ctx, [params[1] as string])
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet selection callbacks
   * @param ctx Callback query context
   * @param params Selection parameters
   */
  async handleWalletSelect(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id || 0
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    // Show wallet details with action buttons
    const keyboard = new InlineKeyboard()
      .text("💰 Balance", `wallet_detail:balance:${walletId}`)
      .text("📊 History", `wallet_detail:history:${walletId}`)
      .row()
      .text("📈 Stats", `wallet_detail:stats:${walletId}`)
      .text("🔑 Export Key", `wallet_detail:export:${walletId}`)
      .row()
      .text("🗑️ Delete", `wallet_detail:delete:${walletId}`)
      .text("🔙 Back to Wallets", "wallet_action:list")

    const { symbol, name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]
    const balance = wallet.balance.toString()
    const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

    await this.updateKeyboard(ctx, "wallet_info", keyboard, {
      name: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address,
      balance,
      symbol,
      chainId: wallet.chainId,
      createdDate,
      createdBy: wallet.createdBy
    })
  }

  /**
   * Handle wallet detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  async handleWalletDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const walletIdStr = params[1]
    if (!walletIdStr) {
      await this.deleteKeyboard(ctx)
      return
    }

    const walletId = parseInt(walletIdStr)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "balance":
        await this.showWalletBalance(ctx, wallet)
        break
      case "history":
        await this.showWalletHistory(ctx, wallet)
        break
      case "stats":
        await this.showWalletStats(ctx, wallet)
        break
      case "export":
        await this.showExportConfirmation(ctx, wallet)
        break
      case "delete":
        await this.showDeleteConfirmation(ctx, wallet)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet delete selection
   * @param ctx Callback query context
   * @param params Selection parameters
   */
  async handleWalletDeleteSelect(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    await this.showDeleteConfirmation(ctx, wallet)
  }

  /**
   * Handle delete confirmation callbacks
   * @param ctx Callback query context
   * @param params Delete parameters
   */
  async handleConfirmDelete(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    const telegramId = ctx.from?.id || 0
    const user = await User.getById(telegramId)

    if (!user) {
      await this.answerCallback(ctx, "callback_user_not_found")
      await this.deleteKeyboard(ctx)
      return
    }

    // Use secure method to get wallet with ownership validation
    const wallet = await Wallet.getByIdForOwner(walletId, user.id)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    const success = await Wallet.remove(walletId, user.id)

    if (success) {
      await this.answerCallback(ctx, "callback_wallet_deleted_success")
      await this.showWalletList(ctx)
    } else {
      await this.answerCallback(ctx, "callback_wallet_delete_failed")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle export confirmation
   * @param ctx Callback query context
   * @param params Export parameters
   */
  async handleExportConfirm(ctx: any, params: string[]): Promise<void> {
    const walletId = parseInt(params[0] as any)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    // Get user ID for ownership validation
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }

    await this.showPrivateKey(ctx, wallet)
  }

  /**
   * Show wallet list with inline keyboard
   * @param ctx Callback query context
   */
  async showWalletList(ctx: any): Promise<void> {
    await this.showWalletListPage(ctx, 0)
  }

  /**
   * Show wallet list page with pagination
   * @param ctx Callback query context
   * @param page Page number (0-based)
   */
  async showWalletListPage(ctx: any, page: number): Promise<void> {
    try {
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.answerCallback(ctx, "callback_user_not_found")
        await this.deleteKeyboard(ctx)
        return
      }

      const wallets = await Wallet.getAllForOwner(user.id)

      if (wallets.length === 0) {
        const keyboard = new InlineKeyboard().text("➕ Create Wallet", "wallet_action:create").text("📥 Import Wallet", "wallet_action:import").row().text("🔙 Back to Menu", "back:main_menu")

        await this.updateKeyboard(ctx, "wallets_empty", keyboard)
        return
      }

      // Create inline keyboard for wallet selection
      const keyboard = new InlineKeyboard()

      // Add wallet buttons (max 5 per page for better UX)
      const walletsPerPage = 5
      const totalPages = Math.ceil(wallets.length / walletsPerPage)
      const currentPage = Math.max(0, Math.min(page, totalPages - 1))
      const startIndex = currentPage * walletsPerPage
      const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

      for (let i = startIndex; i < endIndex; i++) {
        const wallet = wallets[i] as any
        const balance = wallet.balance.toString()
        const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]
        keyboard.text(`💼 ${wallet.name} (${balance} ${symbol})`, `wallet_select:${wallet.id}`).row()
      }

      // Add pagination if needed
      if (totalPages > 1) {
        keyboard.row()
        if (currentPage > 0) {
          keyboard.text("⬅️ Previous", `page:wallets:${currentPage - 1}`)
        }
        keyboard.text(`${currentPage + 1}/${totalPages}`, "noop")
        if (currentPage < totalPages - 1) {
          keyboard.text("➡️ Next", `page:wallets:${currentPage + 1}`)
        }
      }

      // Add action buttons
      keyboard.row().text("➕ Create", "wallet_action:create").text("📥 Import", "wallet_action:import").text("🔙 Menu", "back:main_menu")

      // Format wallet list for display
      const walletList = wallets
        .slice(startIndex, endIndex)
        .map((wallet, index) => {
          const balance = wallet.balance.toString()
          const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]
          return `${startIndex + index + 1}. ${wallet.name}\n   🔗 ${wallet.chain}\n   💰 ${balance} ${symbol}\n   📍 ${wallet.address}`
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "wallets_list", keyboard, {
        walletList,
        totalWallets: wallets.length,
        currentPage: currentPage + 1,
        totalPages
      })
    } catch (error) {
      log.error(`Error showWalletListPage: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show wallet balance details
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletBalance(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

    const balance = wallet.balance.toString()
    const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]
    await this.updateKeyboard(ctx, "balance_display", keyboard, {
      walletName: wallet.name,
      balance,
      symbol,
      chainName: wallet.chain
    })
  }

  /**
   * Show wallet trading history
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletHistory(ctx: any, wallet: any): Promise<void> {
    try {
      const history = await TradingHistory.getForWallet(BigInt(wallet.id), 10)

      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

      if (history.length === 0) {
        await this.updateKeyboard(ctx, "history_empty", keyboard, { walletName: wallet.name })
        return
      }

      const historyText = history
        .map((trade, index) => {
          const operation = trade.operation === "B" ? "🟢 BUY" : "🔴 SELL"
          const status = trade.success ? "✅" : "❌"
          const amount = trade.amount.toString()
          const date = trade.createdAt?.toLocaleDateString() || "Unknown"
          const { symbol } = BlockchainConfig[trade.chain as TypeChainName]

          return `${index + 1}. ${operation} ${status}\n   💰 ${amount} ${symbol}\n   📅 ${date}`
        })
        .join("\n\n")

      await this.updateKeyboard(ctx, "history_display", keyboard, {
        walletName: wallet.name,
        historyText
      })
    } catch (error) {
      log.error(`Error showWalletHistory: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show wallet statistics
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showWalletStats(ctx: any, wallet: any): Promise<void> {
    try {
      const stats = await TradingHistory.getWalletStats(BigInt(wallet.id))

      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
      if (!stats) {
        await this.updateKeyboard(ctx, "stats_empty", keyboard, { walletName: wallet.name })
        return
      }

      const successRate = stats.totalTrades > 0 ? ((stats.successTrades / stats.totalTrades) * 100).toFixed(2) : "0"
      const { symbol } = BlockchainConfig[wallet.chain as TypeChainName]

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        walletName: wallet.name,
        totalTrades: stats.totalTrades,
        successTrades: stats.successTrades,
        failedTrades: stats.failedTrades,
        successRate,
        totalVolume: stats.totalVolume.toString(),
        successVolume: stats.successVolume.toString(),
        symbol,
        buyTrades: stats.buyTrades,
        sellTrades: stats.sellTrades
      })
    } catch (error) {
      log.error(`Error showWalletStats: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show export confirmation with security warning
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showExportConfirmation(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🔑 Yes, Export Key", `export_confirm:${wallet.id}`).text("🔙 Cancel", `wallet_select:${wallet.id}`)

    const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]
    await this.updateKeyboard(ctx, "export_confirmation", keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }

  /**
   * Show private key with security warnings
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showPrivateKey(ctx: any, wallet: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const privateKey = await Wallet.getDecryptedPrivateKey(wallet.id, telegramId, this.passwordWallet)

      if (!privateKey) {
        // Show error if decryption failed or user doesn't own wallet
        const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
        const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]

        await this.updateKeyboard(ctx, "export_error", keyboard, {
          walletName: wallet.name,
          chainName: chainDisplayName,
          address: wallet.address
        })
        return
      }

      // Show the private key with security warnings
      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
      const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]

      await this.updateKeyboard(ctx, "export_private_key", keyboard, {
        walletName: wallet.name,
        chainName: chainDisplayName,
        address: wallet.address,
        privateKey
      })
    } catch (error) {
      log.error(`Error showPrivateKey: ${error}`)

      // Show error message
      const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)
      const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]

      await this.updateKeyboard(ctx, "export_error", keyboard, {
        walletName: wallet.name,
        chainName: chainDisplayName,
        address: wallet.address
      })
    }
  }

  /**
   * Show delete confirmation dialog
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showDeleteConfirmation(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("✅ Yes, Delete", `confirm_delete:${wallet.id}`).text("❌ Cancel", "cancel").row().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

    const { name: chainDisplayName } = BlockchainConfig[wallet.chain as TypeChainName]
    await this.updateKeyboard(ctx, "deletewallet_confirm", keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }
}
