import { <PERSON><PERSON><PERSON><PERSON> } from "../baseHandler"
import { HelpKeyboard } from "../keyboards/help"

/**
 * Help Sections Page
 * Handles help menu and individual help sections
 */
export class HelpSectionsPage extends BaseHandler {
  /**
   * Show help menu
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = HelpKeyboard.create()
    await this.updateKeyboard(ctx, "help", keyboard)
  }

  /**
   * Show wallet help section
   * @param ctx Callback query context
   */
  async showWallets(ctx: any): Promise<void> {
    const keyboard = HelpKeyboard.createBackToHelp()
    await this.updateKeyboard(ctx, "help_wallets", keyboard)
  }

  /**
   * Show trading help section
   * @param ctx Callback query context
   */
  async showTrading(ctx: any): Promise<void> {
    const keyboard = HelpKeyboard.createBackToHelp()
    await this.updateKeyboard(ctx, "help_trading", keyboard)
  }

  /**
   * Show config help section
   * @param ctx Callback query context
   */
  async showConfig(ctx: any): Promise<void> {
    const keyboard = HelpKeyboard.createBackToHelp()
    await this.updateKeyboard(ctx, "help_config", keyboard)
  }
}
