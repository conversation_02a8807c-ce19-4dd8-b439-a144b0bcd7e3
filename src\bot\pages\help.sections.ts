import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON>and<PERSON> } from "../baseHandler"

/**
 * Help Sections Page
 * Handles help menu and individual help sections
 */
export class HelpSectionsPage extends BaseHandler {
  /**
   * Create help keyboard
   */
  private static createKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("💼 Wallet Commands", "help:wallets").text("📊 Trading Commands", "help:trading").row().text("⚙️ Config Commands", "help:config").text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create back to help keyboard
   */
  private static createBackToHelpKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Help", "wallet_action:help").text("🏠 Main Menu", "back:main_menu")
  }
  /**
   * Show help menu
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = HelpSectionsPage.createKeyboard()
    await this.updateKeyboard(ctx, "help", keyboard)
  }

  /**
   * Show wallet help section
   * @param ctx Callback query context
   */
  async showWallets(ctx: any): Promise<void> {
    const keyboard = HelpSectionsPage.createBackToHelpKeyboard()
    await this.updateKeyboard(ctx, "help_wallets", keyboard)
  }

  /**
   * Show trading help section
   * @param ctx Callback query context
   */
  async showTrading(ctx: any): Promise<void> {
    const keyboard = HelpKeyboard.createBackToHelp()
    await this.updateKeyboard(ctx, "help_trading", keyboard)
  }

  /**
   * Show config help section
   * @param ctx Callback query context
   */
  async showConfig(ctx: any): Promise<void> {
    const keyboard = HelpKeyboard.createBackToHelp()
    await this.updateKeyboard(ctx, "help_config", keyboard)
  }
}
