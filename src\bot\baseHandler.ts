import { type CommandContext, Context, InlineKeyboard } from "grammy"
import { log } from "../utils/log"
import { Content } from "../utils/content"
import { Keyv } from "keyv"

const content = new Content()
const cache = new Keyv({ store: new Map(), ttl: 24 * 60 * 60 * 1000 }) // 24jam

/**
 * Base handler class containing core bot functionality
 * Provides session management, keyboard utilities, and base message handling
 */
export class BaseHandler {
  protected passwordWallet = process.env.PASSWORD_WALLET as string

  /**
   * Session Management Methods
   */
  /**
   * Store session data for a user
   * @param userId Telegram user ID
   * @param sessionData Session data object containing state and temporary data
   */
  protected async sessionSet(userId: number, sessionData: Record<string, any>): Promise<void> {
    try {
      await cache.set(`session:${userId}`, sessionData)
    } catch (error) {
      log.error(`Error sessionSet: ${error}`)
    }
  }

  /**
   * Retrieve session data for a user
   * @param userId Telegram user ID
   * @returns Session data object or null if no active session
   */
  protected async sessionGet(userId: number): Promise<Record<string, any> | null> {
    try {
      return (await cache.get(`session:${userId}`)) || null
    } catch (error) {
      log.error(`Error sessionGet: ${error}`)
      return null
    }
  }

  /**
   * Clear session data when operation completes
   * @param userId Telegram user ID
   */
  protected async sessionDelete(userId: number): Promise<void> {
    try {
      await cache.delete(`session:${userId}`)
    } catch (error) {
      log.error(`Error sessionDelete: ${error}`)
    }
  }

  /**
   * Check if user has an active input session
   * @param userId Telegram user ID
   * @returns True if user has an active session
   */
  protected async sessionHas(userId: number): Promise<boolean> {
    try {
      return await cache.has(`session:${userId}`)
    } catch (error) {
      log.error(`Error sessionHas: ${error}`)
      return false
    }
  }

  /**
   * Reusable helper method to reply with content
   * @param ctx Telegram context object
   * @param contentKey Content key to retrieve from content system
   * @param data Optional data object for variable replacement
   * @param options Optional reply options
   * @returns Promise from ctx.reply()
   */
  protected async reply(ctx: CommandContext<Context>, contentKey: string, data?: Record<string, any>, options?: any): Promise<any> {
    return ctx.reply(content.get(contentKey, data), { parse_mode: `HTML`, ...options })
  }

  /**
   * Send message with inline keyboard and track state
   * @param ctx Telegram context object
   * @param contentKey Content key to retrieve from content system
   * @param keyboard Inline keyboard markup
   * @param data Optional data object for variable replacement
   * @returns Promise from ctx.reply()
   */
  protected async replyWithKeyboard(ctx: CommandContext<Context>, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>): Promise<any> {
    try {
      const message = await ctx.reply(content.get(contentKey, data), {
        parse_mode: `HTML`,
        reply_markup: keyboard
      })

      return message
    } catch (error) {
      log.error(`Error replyWithKeyboard: ${error}`)
      return this.reply(ctx, contentKey, data)
    }
  }

  /**
   * Update existing inline keyboard or delete if update fails
   * @param ctx Callback query context
   * @param contentKey Content key for new message text
   * @param keyboard New inline keyboard markup
   * @param data Optional data object for variable replacement
   * @returns Promise indicating success
   */
  protected async updateKeyboard(ctx: any, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>) {
    try {
      await ctx.editMessageText(content.get(contentKey, data), {
        parse_mode: `HTML`,
        reply_markup: keyboard
      })
    } catch (error) {
      await this.replyWithKeyboard(ctx, contentKey, keyboard, data)
    }
  }

  /**
   * Delete inline keyboard message
   * @param ctx Callback query context
   * @returns Promise indicating success
   */
  protected async deleteKeyboard(ctx: any) {
    try {
      await ctx.deleteMessage()
    } catch (error) {
      log.error(`Error deleteKeyboard: ${error}`)
    }
  }

  /**
   * Answer callback query with content
   * @param ctx Callback query context
   * @param contentKey Content key to retrieve from content system
   * @param data Optional data object for variable replacement
   * @returns Promise from ctx.answerCallbackQuery()
   */
  protected async answerCallback(ctx: any, contentKey: string, data?: Record<string, any>): Promise<void> {
    try {
      await ctx.answerCallbackQuery(content.get(contentKey, data))
    } catch (error) {
      log.error(`Error answerCallback: ${error}`)
      // Fallback to empty answer to prevent timeout
      try {
        await ctx.answerCallbackQuery()
      } catch (fallbackError) {
        log.error(`Error answerCallback with fallback: ${fallbackError}`)
      }
    }
  }
}
