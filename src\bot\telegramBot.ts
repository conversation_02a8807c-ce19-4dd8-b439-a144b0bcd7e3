import { log } from "../utils/log"
import { User, Wallet } from "../models"
import { TelegramHandler } from "./handlers/telegram"

// Import all page classes directly
import { MainMenuPage } from "./pages/main.menu"
import { WalletListPage } from "./pages/wallet.list"
import { WalletDetailsPage } from "./pages/wallet.details"
import { WalletBalancePage } from "./pages/wallet.balance"
import { WalletHistoryPage } from "./pages/wallet.history"
import { WalletStatsPage } from "./pages/wallet.stats"
import { WalletCreatePage } from "./pages/wallet.create"
import { WalletImportPage } from "./pages/wallet.import"
import { WalletExportPage } from "./pages/wallet.export"
import { WalletDeletePage } from "./pages/wallet.delete"
import { AccountStatsPage } from "./pages/account.stats"
import { SettingsMainPage } from "./pages/settings.main"
import { HelpSectionsPage } from "./pages/help.sections"
import { ChainInfoPage } from "./pages/chain.info"

/**
 * Production-optimized Telegram bot class
 * Direct page access for improved efficiency and maintainability
 */
export class TelegramBot extends TelegramHandler {
  // Direct page instances for optimal performance
  private mainMenuPage = new MainMenuPage()
  private walletListPage = new WalletListPage()
  private walletDetailsPage = new WalletDetailsPage()
  private walletBalancePage = new WalletBalancePage()
  private walletHistoryPage = new WalletHistoryPage()
  private walletStatsPage = new WalletStatsPage()
  private walletCreatePage = new WalletCreatePage()
  private walletImportPage = new WalletImportPage()
  private walletExportPage = new WalletExportPage()
  private walletDeletePage = new WalletDeletePage()
  private accountStatsPage = new AccountStatsPage()
  private settingsMainPage = new SettingsMainPage()
  private helpSectionsPage = new HelpSectionsPage()
  private chainInfoPage = new ChainInfoPage()

  constructor() {
    super(process.env.TELEGRAM_TOKEN as string)
  }

  /**
   * Override callback query handler
   */
  protected async onCallbackQuery(ctx: any, action: string, params: string[]): Promise<void> {
    switch (action) {
      case "wallet_action":
        await this.handleWalletAction(ctx, params)
        break
      case "wallet_select":
        const walletId = parseInt(params[0] || "0")
        if (!isNaN(walletId) && walletId > 0) {
          const telegramId = ctx.from?.id as number
          await this.walletDetailsPage.show(ctx, walletId, telegramId)
        }
        break
      case "wallet_detail":
        await this.handleWalletDetail(ctx, params)
        break
      case "confirm_delete":
        const deleteWalletId = parseInt(params[0] || "0")
        if (!isNaN(deleteWalletId) && deleteWalletId > 0) {
          const telegramId = ctx.from?.id as number
          await this.walletDeletePage.processDelete(ctx, deleteWalletId, telegramId)
        }
        break
      case "export_confirm":
        const exportWalletId = parseInt(params[0] || "0")
        if (!isNaN(exportWalletId) && exportWalletId > 0) {
          const telegramId = ctx.from?.id as number
          const wallet = await Wallet.getByIdForOwner(exportWalletId, telegramId)
          if (wallet) {
            await this.walletExportPage.showPrivateKey(ctx, wallet, telegramId)
          }
        }
        break
      case "create_wallet":
        if (params[0]) {
          const userId = ctx.from?.id as number
          await this.walletCreatePage.showNamePrompt(ctx, params[0], userId)
        }
        break
      case "import_chain":
        if (params[0]) {
          const userId = ctx.from?.id as number
          await this.walletImportPage.showNamePrompt(ctx, params[0], userId)
        }
        break
      case "help":
        if (params[0]) {
          await this.handleHelpSection(ctx, params[0])
        }
        break
      case "settings":
        const action = params[0]
        switch (action) {
          case "language":
            await this.settingsMainPage.showLanguage(ctx)
            break
          case "notifications":
            await this.settingsMainPage.showNotifications(ctx)
            break
          default:
            await this.deleteKeyboard(ctx)
        }
        break
      case "chain_select":
        if (params[0]) {
          await this.chainInfoPage.show(ctx, params[0])
        }
        break
      case "chain_detail":
        await this.handleChainDetail(ctx, params)
        break
      case "cancel":
        await this.handleCancel(ctx)
        break
      case "back":
        await this.handleBack(ctx, params)
        break
      case "page":
        await this.handlePagination(ctx, params)
        break
      case "noop":
        // No-operation: completely non-interactive button
        break
      case "main_menu":
        await this.mainMenuPage.show(ctx)
        break
      default:
        log.warn(`Unknown callback action: ${action}`)
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Override text message handler
   */
  protected async onTextMessage(ctx: any, user: any): Promise<void> {
    // Show main menu for users without active sessions
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Override no session handler
   */
  protected async onNoSession(ctx: any): Promise<void> {
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Override session input handler
   */
  protected async onSessionInputReceived(ctx: any, userId: number, userInput: string, session: any): Promise<void> {
    switch (session.state) {
      case "waiting_wallet_name":
        await this.walletCreatePage.processCreation(ctx, userId, userInput, session.chain)
        break
      case "waiting_private_key":
        // Process private key input for wallet import
        await this.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        break
      case "waiting_import_details":
        // Handle multi-step import process (wallet name or private key)
        const step = session.step || "wallet_name"
        if (step === "wallet_name") {
          // Process wallet name and move to private key step
          await this.handleWalletNameInput(ctx, userId, userInput, session)
        } else if (step === "private_key") {
          // Process private key input
          await this.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        }
        break
      default:
        log.warn(`Unknown session state: ${session.state}`)
        await this.sessionDelete(userId)
        await this.mainMenuPage.show(ctx)
    }
  }

  /**
   * Handle wallet action callbacks
   * @param ctx Callback query context
   * @param params Action parameters
   */
  private async handleWalletAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "list":
        await this.walletListPage.show(ctx)
        break
      case "create":
        await this.walletCreatePage.show(ctx)
        break
      case "import":
        await this.walletImportPage.show(ctx)
        break
      case "stats":
        await this.accountStatsPage.show(ctx)
        break
      case "settings":
        await this.settingsMainPage.show(ctx)
        break
      case "help":
        await this.helpSectionsPage.show(ctx)
        break
      case "view":
        const walletId = parseInt(params[1] || "0")
        if (!isNaN(walletId) && walletId > 0) {
          const telegramId = ctx.from?.id as number
          await this.walletDetailsPage.show(ctx, walletId, telegramId)
        }
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleWalletDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const walletIdStr = params[1]
    if (!walletIdStr) {
      await this.deleteKeyboard(ctx)
      return
    }

    const walletId = parseInt(walletIdStr)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "balance":
        const telegramId = ctx.from?.id as number
        const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
        if (wallet) {
          await this.walletBalancePage.show(ctx, wallet)
        }
        break
      case "history":
        const telegramId2 = ctx.from?.id as number
        const wallet2 = await Wallet.getByIdForOwner(walletId, telegramId2)
        if (wallet2) {
          await this.walletHistoryPage.show(ctx, wallet2)
        }
        break
      case "stats":
        const telegramId3 = ctx.from?.id as number
        const wallet3 = await Wallet.getByIdForOwner(walletId, telegramId3)
        if (wallet3) {
          await this.walletStatsPage.show(ctx, wallet3)
        }
        break
      case "export":
        const telegramId4 = ctx.from?.id as number
        const wallet4 = await Wallet.getByIdForOwner(walletId, telegramId4)
        if (wallet4) {
          await this.walletExportPage.showConfirmation(ctx, wallet4)
        }
        break
      case "delete":
        const telegramId5 = ctx.from?.id as number
        const wallet5 = await Wallet.getByIdForOwner(walletId, telegramId5)
        if (wallet5) {
          await this.walletDeletePage.showConfirmation(ctx, wallet5)
        }
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle chain detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleChainDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const chain = params[1]

    if (!chain) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "fees":
        await this.chainInfoPage.showFees(ctx, chain)
        break
      case "stats":
        await this.chainInfoPage.showStats(ctx, chain)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle cancel callbacks
   * @param ctx Callback query context
   */
  private async handleCancel(ctx: any): Promise<void> {
    const userId = ctx.from?.id as number

    // Clear any active session
    await this.sessionDelete(userId)

    await this.answerCallback(ctx, "callback_cancelled")
    await this.deleteKeyboard(ctx)

    // Show main menu after cancellation
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Handle back navigation callbacks
   * @param ctx Callback query context
   * @param params Navigation parameters
   */
  private async handleBack(ctx: any, params: string[]): Promise<void> {
    const destination = params[0]

    switch (destination) {
      case "main_menu":
        await this.mainMenuPage.show(ctx)
        break
      case "wallets":
        await this.walletListPage.show(ctx)
        break
      case "help":
        await this.helpSectionsPage.show(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle pagination callbacks
   * @param ctx Callback query context
   * @param params Pagination parameters
   */
  private async handlePagination(ctx: any, params: string[]): Promise<void> {
    const type = params[0]
    const page = parseInt(params[1] || "0")

    if (isNaN(page)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (type) {
      case "wallets":
        await this.walletListPage.show(ctx, page)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle help section navigation
   * @param ctx Callback query context
   * @param section Help section name
   */
  private async handleHelpSection(ctx: any, section: string): Promise<void> {
    switch (section) {
      case "wallets":
        await this.helpSectionsPage.showWallets(ctx)
        break
      case "trading":
        await this.helpSectionsPage.showTrading(ctx)
        break
      case "config":
        await this.helpSectionsPage.showConfig(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet name input during import process
   * @param ctx Message context
   * @param userId User ID
   * @param walletName Wallet name input
   * @param session Session data
   */
  private async handleWalletNameInput(ctx: any, userId: number, walletName: string, session: any): Promise<void> {
    // Validate wallet name
    if (walletName.length > 32) {
      await this.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(walletName)) {
      await this.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    // Check if wallet name already exists
    const user = await User.getById(userId)
    if (!user) {
      await this.sessionDelete(userId)
      await this.mainMenuPage.show(ctx)
      return
    }

    const existingWallet = await Wallet.getByName(user.id, walletName)
    if (existingWallet) {
      await this.walletImportPage.showFailure(ctx, session.chain, "duplicate_name")
      return
    }

    // Update session and show private key prompt
    await this.sessionSet(userId, {
      ...session,
      walletName: walletName,
      step: "private_key"
    })

    await this.walletImportPage.showPrivateKeyPrompt(ctx, session.chain, walletName, userId)
  }
}
