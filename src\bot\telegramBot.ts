import { log } from "../utils/log"
import { TelegramHandler } from "./handlers/telegramHandler"
import { PageRouter } from "./pages/pageRouter"

/**
 * Page-based Telegram bot class
 * Integrates page-based architecture with specialized handlers
 */
export class TelegramBot extends TelegramHandler {
  // Page router instance
  private pageRouter = new PageRouter()

  constructor() {
    super(process.env.TELEGRAM_TOKEN as string)
  }

  /**
   * Override callback query handler
   */
  protected async onCallbackQuery(ctx: any, action: string, params: string[]): Promise<void> {
    switch (action) {
      case "wallet_action":
        await this.handleWalletAction(ctx, params)
        break
      case "wallet_select":
        const walletId = parseInt(params[0] || "0")
        if (!isNaN(walletId) && walletId > 0) {
          await this.pageRouter.showWalletDetails(ctx, walletId)
        }
        break
      case "wallet_detail":
        await this.handleWalletDetail(ctx, params)
        break
      case "confirm_delete":
        const deleteWalletId = parseInt(params[0] || "0")
        if (!isNaN(deleteWalletId) && deleteWalletId > 0) {
          await this.pageRouter.processWalletDelete(ctx, deleteWalletId)
        }
        break
      case "export_confirm":
        const exportWalletId = parseInt(params[0] || "0")
        if (!isNaN(exportWalletId) && exportWalletId > 0) {
          await this.pageRouter.showWalletExportPrivateKey(ctx, exportWalletId)
        }
        break
      case "create_wallet":
        if (params[0]) {
          await this.pageRouter.showWalletCreateNamePrompt(ctx, params[0])
        }
        break
      case "import_chain":
        if (params[0]) {
          await this.pageRouter.showWalletImportNamePrompt(ctx, params[0])
        }
        break
      case "help":
        if (params[0]) {
          await this.pageRouter.showHelpSection(ctx, params[0])
        }
        break
      case "settings":
        await this.handleSettingsAction(ctx, params)
        break
      case "chain_select":
        if (params[0]) {
          await this.pageRouter.showChainInfo(ctx, params[0])
        }
        break
      case "chain_detail":
        await this.handleChainDetail(ctx, params)
        break
      case "cancel":
        await this.handleCancel(ctx)
        break
      case "back":
        await this.handleBack(ctx, params)
        break
      case "page":
        await this.handlePagination(ctx, params)
        break
      case "noop":
        // No-operation: completely non-interactive button
        break
      case "main_menu":
        await this.pageRouter.showMainMenu(ctx)
        break
      default:
        log.warn(`Unknown callback action: ${action}`)
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Override text message handler
   */
  protected async onTextMessage(ctx: any, user: any): Promise<void> {
    // Show main menu for users without active sessions
    await this.pageRouter.showMainMenu(ctx)
  }

  /**
   * Override no session handler
   */
  protected async onNoSession(ctx: any): Promise<void> {
    await this.pageRouter.showMainMenu(ctx)
  }

  /**
   * Override session input handler
   */
  protected async onSessionInputReceived(ctx: any, userId: number, userInput: string, session: any): Promise<void> {
    switch (session.state) {
      case "waiting_wallet_name":
        await this.pageRouter.processWalletCreation(ctx, userId, userInput, session)
        break
      case "waiting_private_key":
        await this.pageRouter.processWalletImportPrivateKey(ctx, userId, userInput, session)
        break
      case "waiting_import_details":
        await this.pageRouter.processWalletImportDetails(ctx, userId, userInput, session)
        break
      default:
        log.warn(`Unknown session state: ${session.state}`)
        await this.sessionDelete(userId)
        await this.pageRouter.showMainMenu(ctx)
    }
  }

  /**
   * Handle wallet action callbacks
   * @param ctx Callback query context
   * @param params Action parameters
   */
  private async handleWalletAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "list":
        await this.pageRouter.showWalletList(ctx)
        break
      case "create":
        await this.pageRouter.showWalletCreate(ctx)
        break
      case "import":
        await this.pageRouter.showWalletImport(ctx)
        break
      case "stats":
        await this.pageRouter.showAccountStats(ctx)
        break
      case "settings":
        await this.pageRouter.showSettings(ctx)
        break
      case "help":
        await this.pageRouter.showHelp(ctx)
        break
      case "view":
        const walletId = parseInt(params[1] || "0")
        if (!isNaN(walletId) && walletId > 0) {
          await this.pageRouter.showWalletDetails(ctx, walletId)
        }
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleWalletDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const walletIdStr = params[1]
    if (!walletIdStr) {
      await this.deleteKeyboard(ctx)
      return
    }

    const walletId = parseInt(walletIdStr)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "balance":
        await this.pageRouter.showWalletBalance(ctx, walletId)
        break
      case "history":
        await this.pageRouter.showWalletHistory(ctx, walletId)
        break
      case "stats":
        await this.pageRouter.showWalletStats(ctx, walletId)
        break
      case "export":
        await this.pageRouter.showWalletExportConfirmation(ctx, walletId)
        break
      case "delete":
        await this.pageRouter.showWalletDeleteConfirmation(ctx, walletId)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle settings actions
   * @param ctx Callback query context
   * @param params Settings parameters
   */
  private async handleSettingsAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "language":
        await this.pageRouter.showSettingsLanguage(ctx)
        break
      case "notifications":
        await this.pageRouter.showSettingsNotifications(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle chain detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleChainDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const chain = params[1]

    if (!chain) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "fees":
        await this.pageRouter.showChainFees(ctx, chain)
        break
      case "stats":
        await this.pageRouter.showChainStats(ctx, chain)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle cancel callbacks
   * @param ctx Callback query context
   */
  private async handleCancel(ctx: any): Promise<void> {
    const userId = ctx.from?.id as number

    // Clear any active session
    await this.sessionDelete(userId)

    await this.answerCallback(ctx, "callback_cancelled")
    await this.deleteKeyboard(ctx)

    // Show main menu after cancellation
    await this.pageRouter.showMainMenu(ctx)
  }

  /**
   * Handle back navigation callbacks
   * @param ctx Callback query context
   * @param params Navigation parameters
   */
  private async handleBack(ctx: any, params: string[]): Promise<void> {
    const destination = params[0]

    switch (destination) {
      case "main_menu":
        await this.pageRouter.showMainMenu(ctx)
        break
      case "wallets":
        await this.pageRouter.showWalletList(ctx)
        break
      case "help":
        await this.pageRouter.showHelp(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle pagination callbacks
   * @param ctx Callback query context
   * @param params Pagination parameters
   */
  private async handlePagination(ctx: any, params: string[]): Promise<void> {
    const type = params[0]
    const page = parseInt(params[1] || "0")

    if (isNaN(page)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (type) {
      case "wallets":
        await this.pageRouter.showWalletList(ctx, page)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }
}
