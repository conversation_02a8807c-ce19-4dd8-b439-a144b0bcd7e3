import { Bo<PERSON>, InlineKeyboard } from "grammy"
import { log } from "../utils/log"
import { User } from "../models"
import { BaseHandler } from "./baseHandler"
import { PageRouter } from "./pages/pageRouter"

/**
 * Modular Telegram bot class
 * Integrates all specialized modules while maintaining existing API and functionality
 */
export class TelegramBot extends BaseHandler {
  private bot = new Bot(process.env.TELEGRAM_TOKEN as string)

  // Page router instance
  private pageRouter = new PageRouter()

  constructor() {
    super()

    // Register callback query handlers for inline keyboards
    this.bot.on("callback_query:data", this.handleCallbackQuery.bind(this))

    // Handle any text message to show main menu (entry point for new users)
    this.bot.on("message:text", this.handleTextMessage.bind(this))

    // Handled error
    this.bot.catch((err) => {
      log.error(JSON.stringify(err))
    })

    // Started bot
    this.bot.start({
      onStart(botInfo) {
        log.info(`Telegram bot started: ${botInfo.username}`)
      }
    })
  }

  /**
   * Handle callback queries from inline keyboards
   * @param ctx Callback query context
   */
  private async handleCallbackQuery(ctx: any): Promise<void> {
    try {
      await ctx.answerCallbackQuery()

      const data = ctx.callbackQuery.data
      const [action, ...params] = data.split(":")

      switch (action) {
        case "wallet_action":
          await this.handleWalletAction(ctx, params)
          break
        case "wallet_select":
          const walletId = parseInt(params[0])
          if (!isNaN(walletId)) {
            await this.pageRouter.showWalletDetails(ctx, walletId)
          }
          break
        case "wallet_detail":
          await this.handleWalletDetail(ctx, params)
          break
        case "confirm_delete":
          const deleteWalletId = parseInt(params[0])
          if (!isNaN(deleteWalletId)) {
            await this.pageRouter.processWalletDelete(ctx, deleteWalletId)
          }
          break
        case "export_confirm":
          const exportWalletId = parseInt(params[0])
          if (!isNaN(exportWalletId)) {
            await this.pageRouter.showWalletExportPrivateKey(ctx, exportWalletId)
          }
          break
        case "create_wallet":
          await this.pageRouter.showWalletCreateNamePrompt(ctx, params[0])
          break
        case "import_chain":
          await this.pageRouter.showWalletImportNamePrompt(ctx, params[0])
          break
        case "help":
          await this.pageRouter.showHelpSection(ctx, params[0])
          break
        case "settings":
          await this.handleSettingsAction(ctx, params)
          break
        case "chain_select":
          await this.pageRouter.showChainInfo(ctx, params[0])
          break
        case "chain_detail":
          await this.handleChainDetail(ctx, params)
          break
        case "cancel":
          await this.handleCancel(ctx)
          break
        case "back":
          await this.handleBack(ctx, params)
          break
        case "page":
          await this.handlePagination(ctx, params)
          break
        case "noop":
          // No-operation: completely non-interactive button
          break
        case "main_menu":
          await this.pageRouter.showMainMenu(ctx)
          break
        default:
          log.warn(`Unknown callback action: ${action}`)
          await this.deleteKeyboard(ctx)
      }
    } catch (error) {
      log.error(`Error handleCallbackQuery: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle text messages - check for active sessions or show main menu
   * @param ctx Message context from Grammy
   */
  private async handleTextMessage(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        const keyboard = new InlineKeyboard().text("🔄 Try Again", "main_menu")
        await this.replyWithKeyboard(ctx, "user_creation_failed", keyboard)
        return
      }

      // Check if user has an active session
      const hasSession = await this.sessionHas(telegramId)
      if (hasSession) {
        await this.handleSessionInput(ctx, telegramId)
        return
      }

      // No active session - show main menu
      await this.pageRouter.showMainMenu(ctx)
    } catch (error) {
      log.error(`Error handleTextMessage: ${error}`)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "general_error", keyboard)
    }
  }

  /**
   * Handle user text input based on active session state
   * @param ctx Message context from Grammy
   * @param userId Telegram user ID
   */
  private async handleSessionInput(ctx: any, userId: number): Promise<void> {
    try {
      const session = await this.sessionGet(userId)
      if (!session || !session.state) {
        await this.sessionDelete(userId)
        await this.pageRouter.showMainMenu(ctx)
        return
      }

      const userInput = ctx.message?.text?.trim()
      if (!userInput) {
        const keyboard = new InlineKeyboard().text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "input_invalid_empty", keyboard)
        return
      }

      switch (session.state) {
        case "waiting_wallet_name":
          await this.pageRouter.processWalletCreation(ctx, userId, userInput, session)
          break
        case "waiting_private_key":
          await this.pageRouter.processWalletImportPrivateKey(ctx, userId, userInput, session)
          break
        case "waiting_import_details":
          await this.pageRouter.processWalletImportDetails(ctx, userId, userInput, session)
          break
        default:
          log.warn(`Unknown session state: ${session.state}`)
          await this.sessionDelete(userId)
          await this.pageRouter.showMainMenu(ctx)
      }
    } catch (error) {
      log.error(`Error handleSessionInput: ${error}`)
      await this.sessionDelete(userId)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "session_error", keyboard)
    }
  }

  /**
   * Handle wallet action callbacks
   * @param ctx Callback query context
   * @param params Action parameters
   */
  private async handleWalletAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "list":
        await this.pageRouter.showWalletList(ctx)
        break
      case "create":
        await this.pageRouter.showWalletCreate(ctx)
        break
      case "import":
        await this.pageRouter.showWalletImport(ctx)
        break
      case "stats":
        await this.pageRouter.showAccountStats(ctx)
        break
      case "settings":
        await this.pageRouter.showSettings(ctx)
        break
      case "help":
        await this.pageRouter.showHelp(ctx)
        break
      case "view":
        const walletId = parseInt(params[1] || "0")
        if (!isNaN(walletId) && walletId > 0) {
          await this.pageRouter.showWalletDetails(ctx, walletId)
        }
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle wallet detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleWalletDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const walletIdStr = params[1]
    if (!walletIdStr) {
      await this.deleteKeyboard(ctx)
      return
    }

    const walletId = parseInt(walletIdStr)
    if (isNaN(walletId)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "balance":
        await this.pageRouter.showWalletBalance(ctx, walletId)
        break
      case "history":
        await this.pageRouter.showWalletHistory(ctx, walletId)
        break
      case "stats":
        await this.pageRouter.showWalletStats(ctx, walletId)
        break
      case "export":
        await this.pageRouter.showWalletExportConfirmation(ctx, walletId)
        break
      case "delete":
        await this.pageRouter.showWalletDeleteConfirmation(ctx, walletId)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle settings actions
   * @param ctx Callback query context
   * @param params Settings parameters
   */
  private async handleSettingsAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "language":
        await this.pageRouter.showSettingsLanguage(ctx)
        break
      case "notifications":
        await this.pageRouter.showSettingsNotifications(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle chain detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  private async handleChainDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const chain = params[1]

    if (!chain) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (action) {
      case "fees":
        await this.pageRouter.showChainFees(ctx, chain)
        break
      case "stats":
        await this.pageRouter.showChainStats(ctx, chain)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle cancel callbacks
   * @param ctx Callback query context
   */
  private async handleCancel(ctx: any): Promise<void> {
    const userId = ctx.from?.id as number

    // Clear any active session
    await this.sessionDelete(userId)

    await this.answerCallback(ctx, "callback_cancelled")
    await this.deleteKeyboard(ctx)

    // Show main menu after cancellation
    await this.pageRouter.showMainMenu(ctx)
  }

  /**
   * Handle back navigation callbacks
   * @param ctx Callback query context
   * @param params Navigation parameters
   */
  private async handleBack(ctx: any, params: string[]): Promise<void> {
    const destination = params[0]

    switch (destination) {
      case "main_menu":
        await this.pageRouter.showMainMenu(ctx)
        break
      case "wallets":
        await this.pageRouter.showWalletList(ctx)
        break
      case "help":
        await this.pageRouter.showHelp(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle pagination callbacks
   * @param ctx Callback query context
   * @param params Pagination parameters
   */
  private async handlePagination(ctx: any, params: string[]): Promise<void> {
    const type = params[0]
    const page = parseInt(params[1] || "0")

    if (isNaN(page)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (type) {
      case "wallets":
        await this.pageRouter.showWalletList(ctx, page)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }
}
