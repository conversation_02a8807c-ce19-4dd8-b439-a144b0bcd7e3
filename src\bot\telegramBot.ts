import { log } from "../utils/log"
import { User, Wallet } from "../models"
import { TelegramHandler } from "./handlers/telegram"

// Import all page classes directly
import { MainMenuPage } from "./pages/main.menu"
import { WalletListPage } from "./pages/wallet.list"
import { WalletDetailsPage } from "./pages/wallet.details"
import { WalletBalancePage } from "./pages/wallet.balance"
import { WalletHistoryPage } from "./pages/wallet.history"
import { WalletStatsPage } from "./pages/wallet.stats"
import { WalletCreatePage } from "./pages/wallet.create"
import { WalletImportPage } from "./pages/wallet.import"
import { WalletExportPage } from "./pages/wallet.export"
import { WalletDeletePage } from "./pages/wallet.delete"
import { AccountStatsPage } from "./pages/account.stats"
import { SettingsMainPage } from "./pages/settings.main"
import { HelpSectionsPage } from "./pages/help.sections"
import { ChainInfoPage } from "./pages/chain.info"

/**
 * Production-optimized Telegram bot class
 * Direct page access for improved efficiency and maintainability
 */
export class TelegramBot extends TelegramHandler {
  // Direct page instances for optimal performance
  private mainMenuPage = new MainMenuPage()
  private walletListPage = new WalletListPage()
  private walletDetailsPage = new WalletDetailsPage()
  private walletBalancePage = new WalletBalancePage()
  private walletHistoryPage = new WalletHistoryPage()
  private walletStatsPage = new WalletStatsPage()
  private walletCreatePage = new WalletCreatePage()
  private walletImportPage = new WalletImportPage()
  private walletExportPage = new WalletExportPage()
  private walletDeletePage = new WalletDeletePage()
  private accountStatsPage = new AccountStatsPage()
  private settingsMainPage = new SettingsMainPage()
  private helpSectionsPage = new HelpSectionsPage()
  private chainInfoPage = new ChainInfoPage()

  constructor() {
    super(process.env.TELEGRAM_TOKEN as string)
  }

  /**
   * Override callback query handler with pure callback data strings
   */
  protected async onCallbackQuery(ctx: any, callbackData: string): Promise<void> {
    // Handle wallet actions
    if (callbackData === "wallet_action:list") {
      await this.walletListPage.show(ctx)
    } else if (callbackData === "wallet_action:create") {
      await this.walletCreatePage.show(ctx)
    } else if (callbackData === "wallet_action:import") {
      await this.walletImportPage.show(ctx)
    } else if (callbackData === "wallet_action:stats") {
      await this.accountStatsPage.show(ctx)
    } else if (callbackData === "wallet_action:settings") {
      await this.settingsMainPage.show(ctx)
    } else if (callbackData === "wallet_action:help") {
      await this.helpSectionsPage.show(ctx)

      // Handle wallet selection
    } else if (callbackData.startsWith("wallet_select:")) {
      const walletId = parseInt(callbackData.split(":")[1] || "0")
      if (!isNaN(walletId) && walletId > 0) {
        const telegramId = ctx.from?.id as number
        await this.walletDetailsPage.show(ctx, walletId, telegramId)
      }
    } else if (callbackData.startsWith("wallet_detail:")) {
      const userId = ctx.from?.id as number
      const parts = callbackData.split(":")
      const route = parts[1]
      const walletId = parseInt(parts[2] || "0")
      const wallet = await Wallet.getByIdForOwner(walletId, userId)
      if (route === "balance") await this.walletBalancePage.show(ctx, wallet)
      if (route === "history") await this.walletHistoryPage.show(ctx, wallet)
      if (route === "stats") await this.walletStatsPage.show(ctx, wallet)
      if (route === "export") await this.walletExportPage.showConfirmation(ctx, wallet)
      if (route === "delete") await this.walletDeletePage.showConfirmation(ctx, wallet)

      // Handle delete confirmation
    } else if (callbackData.startsWith("confirm_delete:")) {
      const deleteWalletId = parseInt(callbackData.split(":")[1] || "0")
      if (!isNaN(deleteWalletId) && deleteWalletId > 0) {
        const telegramId = ctx.from?.id as number
        await this.walletDeletePage.processDelete(ctx, deleteWalletId, telegramId)
      }

      // Handle export confirmation
    } else if (callbackData.startsWith("export_confirm:")) {
      const exportWalletId = parseInt(callbackData.split(":")[1] || "0")
      if (!isNaN(exportWalletId) && exportWalletId > 0) {
        const telegramId = ctx.from?.id as number
        const wallet = await Wallet.getByIdForOwner(exportWalletId, telegramId)
        if (wallet) {
          await this.walletExportPage.showPrivateKey(ctx, wallet, telegramId)
        }
      }

      // Handle wallet creation with chain
    } else if (callbackData.startsWith("create_wallet:")) {
      const chain = callbackData.split(":")[1]
      if (chain) {
        const userId = ctx.from?.id as number
        await this.walletCreatePage.showNamePrompt(ctx, chain, userId)
      }

      // Handle wallet import with chain
    } else if (callbackData.startsWith("import_chain:")) {
      const chain = callbackData.split(":")[1]
      if (chain) {
        const userId = ctx.from?.id as number
        await this.walletImportPage.showNamePrompt(ctx, chain, userId)
      }

      // Handle help sections
    } else if (callbackData == "help:wallets") {
      await this.helpSectionsPage.showWallets(ctx)
    } else if (callbackData == "help:trading") {
      await this.helpSectionsPage.showTrading(ctx)
    } else if (callbackData == "help:config") {
      await this.helpSectionsPage.showConfig(ctx)

      // Handle settings
    } else if (callbackData === "settings:language") {
      await this.settingsMainPage.showLanguage(ctx)
    } else if (callbackData === "settings:notifications") {
      await this.settingsMainPage.showNotifications(ctx)

      // Handle chain selection
    } else if (callbackData.startsWith("chain_select:")) {
      const chain = callbackData.split(":")[1]
      if (chain) {
        await this.chainInfoPage.show(ctx, chain)
      }

      // Handle chain details
    } else if (callbackData.startsWith("chain_detail:fees")) {
      const chain = callbackData.split(":")[2] as string
      await this.chainInfoPage.showFees(ctx, chain)
    } else if (callbackData.startsWith("chain_detail:stats")) {
      const chain = callbackData.split(":")[2] as string
      await this.chainInfoPage.showStats(ctx, chain)

      // Handle navigation
    } else if (callbackData === "cancel") {
      const userId = ctx.from?.id as number
      await this.sessionDelete(userId) // Clear any active session
      await this.answerCallback(ctx, "callback_cancelled") // Acknowledge the callback
      await this.deleteKeyboard(ctx) // Delete the keyboard
      await this.mainMenuPage.show(ctx) // Show main menu after cancellation
    } else if (callbackData === "back:main_menu") {
      await this.mainMenuPage.show(ctx)
    } else if (callbackData === "back:wallets") {
      await this.walletListPage.show(ctx)
    } else if (callbackData === "back:help") {
      await this.helpSectionsPage.show(ctx)
    } else if (callbackData === "page:wallets") {
      const parts = callbackData.split(":")
      const page = parseInt(parts[2] || "0")
      await this.walletListPage.show(ctx, page)
    } else if (callbackData === "noop") {
      return // No-operation: completely non-interactive button
    } else if (callbackData === "main_menu") {
      await this.mainMenuPage.show(ctx)
    } else {
      log.warn(`Unknown callback data: ${callbackData}`)
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Override text message handler
   */
  protected async onTextMessage(ctx: any, user: any): Promise<void> {
    // Show main menu for users without active sessions
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Override no session handler
   */
  protected async onNoSession(ctx: any): Promise<void> {
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Override session input handler
   */
  protected async onSessionInputReceived(ctx: any, userId: number, userInput: string, session: any): Promise<void> {
    switch (session.state) {
      case "waiting_wallet_name":
        await this.walletCreatePage.processCreation(ctx, userId, userInput, session.chain)
        break
      case "waiting_private_key":
        // Process private key input for wallet import
        await this.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        break
      case "waiting_import_details":
        // Handle multi-step import process (wallet name or private key)
        const step = session.step || "wallet_name"
        if (step === "wallet_name") {
          // Process wallet name and move to private key step
          await this.handleWalletNameInput(ctx, userId, userInput, session)
        } else if (step === "private_key") {
          // Process private key input
          await this.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        }
        break
      default:
        log.warn(`Unknown session state: ${session.state}`)
        await this.sessionDelete(userId)
        await this.mainMenuPage.show(ctx)
    }
  }

  /**
   * Handle wallet name input during import process
   * @param ctx Message context
   * @param userId User ID
   * @param walletName Wallet name input
   * @param session Session data
   */
  private async handleWalletNameInput(ctx: any, userId: number, walletName: string, session: any): Promise<void> {
    // Validate wallet name
    if (walletName.length > 32) {
      await this.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(walletName)) {
      await this.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    // Check if wallet name already exists
    const user = await User.getById(userId)
    if (!user) {
      await this.sessionDelete(userId)
      await this.mainMenuPage.show(ctx)
      return
    }

    const existingWallet = await Wallet.getByName(user.id, walletName)
    if (existingWallet) {
      await this.walletImportPage.showFailure(ctx, session.chain, "duplicate_name")
      return
    }

    // Update session and show private key prompt
    await this.sessionSet(userId, {
      ...session,
      walletName: walletName,
      step: "private_key"
    })

    await this.walletImportPage.showPrivateKeyPrompt(ctx, session.chain, walletName, userId)
  }
}
