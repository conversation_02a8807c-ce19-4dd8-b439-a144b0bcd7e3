import { defineConfig } from "drizzle-kit"
import { join } from "node:path"

const isUrlLocalPath = process.env.DATABASE_URL?.startsWith(`./`)
export default defineConfig({
  dialect: `postgresql`,
  out: `./drizzle`,
  schema: `./src/db/schema`,
  verbose: true,
  casing: "snake_case",
  dbCredentials: {
    url: isUrlLocalPath ? join(process.cwd(), process.env.DATABASE_URL as string) : (process.env.DATABASE_URL as string)
  },
  ...(isUrlLocalPath ? { driver: `pglite` } : {})
})
