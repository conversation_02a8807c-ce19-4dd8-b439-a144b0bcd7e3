import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "./baseHandler"
import { Wallet, TradingHistory } from "../models"
import { dataChainName, type <PERSON><PERSON>hainName } from "../data"
import { BlockchainConfig } from "../blockchain/config"
import { log } from "../utils/log"

/**
 * Statistics management module
 * Handles all statistics and analytics functionality including user stats, wallet stats, and chain stats
 */
export class StatisticsManager extends BaseHandler {
  /**
   * Show comprehensive trading analytics directly
   * @param ctx Callback query context
   */
  async showUserStats(ctx: any): Promise<void> {
    // Directly call consolidated stats display
    await this.handleStatsDisplay(ctx)
  }

  /**
   * Handle comprehensive statistics display
   * @param ctx Callback query context
   */
  async handleStatsDisplay(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      // const user = await User.getById(telegramId)

      const wallets = await Wallet.getAllForOwner(telegramId)
      const totalWallets = wallets.length

      // Calculate comprehensive trading statistics across all wallets
      let totalTrades = 0
      let totalSuccessful = 0
      let totalFailed = 0
      let totalVolume = BigInt(0)
      let totalSuccessVolume = BigInt(0)
      let totalBuyTrades = 0
      let totalSellTrades = 0

      // Get statistics for each chain
      const chainStatsArray: any[] = []

      for (const chain of dataChainName) {
        const stats = await TradingHistory.getChainStats(chain)
        if (stats && stats.totalTrades > 0) {
          chainStatsArray.push({
            chainName: chain,
            ...stats,
            successRate: ((stats.successTrades / stats.totalTrades) * 100).toFixed(2)
          })

          // Add to overall totals
          totalTrades += stats.totalTrades
          totalSuccessful += stats.successTrades
          totalFailed += stats.failedTrades
          totalVolume += stats.totalVolume
          totalSuccessVolume += stats.successVolume
          totalBuyTrades += stats.buyTrades
          totalSellTrades += stats.sellTrades
        }
      }

      const overallSuccessRate = totalTrades > 0 ? ((totalSuccessful / totalTrades) * 100).toFixed(2) : "0"
      const activeChains = chainStatsArray.length

      // Create network performance summary string
      const networkPerformance =
        chainStatsArray.length > 0
          ? chainStatsArray
              .map(({ chainName, totalTrades, successRate, uniqueWallets }) => `• <b>${chainName}:</b> ${totalTrades} trades | ${successRate}% success | ${uniqueWallets} wallets`)
              .join("\n")
          : "• No trading activity found across networks"

      // Create buy/sell ratio string
      const buyToSellRatio = totalSellTrades > 0 ? `${totalBuyTrades}:${totalSellTrades}` : totalBuyTrades > 0 ? "All Buy Orders" : "No Trades"

      const keyboard = new InlineKeyboard().text("🔙 Back to Menu", "back:main_menu")

      // Create portfolio status message
      const portfolioStatus =
        totalWallets > 0
          ? `• Managing ${totalWallets} wallet(s) across ${activeChains} network(s)\n• Real-time balance tracking and monitoring\n• Multi-chain portfolio diversification`
          : "• No wallets found - create your first wallet to start trading\n• Import existing wallets to manage your portfolio\n• Start building your multi-chain trading strategy"

      // Create trading insights
      const successRateNum = parseFloat(overallSuccessRate)
      const tradingInsights =
        totalTrades > 0
          ? `• Success rate of ${overallSuccessRate}% indicates ${successRateNum >= 70 ? "strong" : successRateNum >= 50 ? "moderate" : "developing"} trading performance\n• ${
              totalBuyTrades > totalSellTrades ? "Buy-focused" : totalSellTrades > totalBuyTrades ? "Sell-focused" : "Balanced"
            } trading strategy detected\n• Active across ${activeChains} blockchain network(s)\n• Total trading volume: ${totalVolume.toString()}`
          : "• No trading activity detected yet\n• Start executing trades to see performance analytics\n• Monitor success rates and optimize your strategy\n• Track volume and profitability across networks"

      await this.updateKeyboard(ctx, "stats_display", keyboard, {
        totalWallets,
        totalTrades,
        totalSuccessful,
        totalFailed,
        overallSuccessRate,
        totalVolume: totalVolume.toString(),
        totalSuccessVolume: totalSuccessVolume.toString(),
        totalBuyTrades,
        totalSellTrades,
        activeChains,
        networkPerformance,
        buyToSellRatio,
        portfolioStatus,
        tradingInsights,
        username: ctx.from?.username || "Unknown"
      })
    } catch (error) {
      log.error(`Error handleStatsDisplay: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle chain detail actions
   * @param ctx Callback query context
   * @param params Detail parameters
   */
  async handleChainDetail(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    const chain = params[1] as TypeChainName

    switch (action) {
      case "stats":
        await this.showChainStats(ctx, chain)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show chain statistics
   * @param ctx Callback query context
   * @param chain Chain name
   */
  private async showChainStats(ctx: any, chain: TypeChainName): Promise<void> {
    const keyboard = new InlineKeyboard()
      .text("💰 View Fees", `chain_detail:fees:${chain}`)
      .text("🔙 Back to Chain", `chain_select:${chain}:info`)

    const { chainId, symbol, name } = BlockchainConfig[chain]

    // For now, show basic chain info as stats
    await this.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: name,
      symbol,
      chainId,
      chain
    })
  }
}
