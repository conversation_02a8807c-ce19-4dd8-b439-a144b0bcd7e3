// import { db } from "./src/db/index"

// // Get all tables and their data
// const tables = await db.execute(
//   `SELECT table_name
//    FROM information_schema.tables
//    WHERE table_schema = 'public'`
// )

// // Fetch data from each table
// for (const table of (tables as any).rows) {
//   const tableName = table.table_name
//   console.log(`\n=== Data from ${tableName} ===`)
//   const { rows }: any = await db.execute(`SELECT * FROM ${tableName}`)
//   console.log(rows)
// }
// // console.log(tables)

// import { Bot, type Context } from "grammy"
// import { type Conversation, type ConversationFlavor, conversations, createConversation } from "@grammyjs/conversations"

// type MyContext = ConversationFlavor<Context>
// type MyConversationContext = Context

// type MyConversation = Conversation<MyContext, MyConversationContext>

// const bot = new Bot<MyContext>(process.env.TELEGRAM_TOKEN as string)

// /** Defines the conversation */
// async function greeting(conversation: MyConversation, ctx: MyConversationContext) {
//   await ctx.reply("Hi there! What is your name?")
//   const { message } = await conversation.wait()
//   await ctx.reply(`Welcome to the chat, ${message?.text}!`)
// }

// bot.use(conversations())
// bot.use(createConversation(greeting, "greeting"))

// bot.command("enter", async (ctx) => {
//   await ctx.reply("Entering conversation!")
//   // enter the function "greeting" you declared
//   await ctx.conversation.enter("greeting")
// })

// bot.command("start", (ctx) => ctx.reply("Hi! Send /enter"))
// bot.use((ctx) => ctx.reply("What a nice update."))

// bot.start()

import { Keyv } from "keyv"
import { setTimeout } from "node:timers/promises"
