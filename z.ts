// import { eq } from "drizzle-orm"
// import { db } from "./src/db/index"
// import { tableWallets } from "./src/db/schema/wallets"

// // Get all tables and their data
// const tables = await db.execute(
//   `SELECT table_name
//    FROM information_schema.tables
//    WHERE table_schema = 'public'`
// )

// for (const table of (tables as any).rows) {
//   const tableName = table.table_name
//   console.log(`\n=== Data from ${tableName} ===`)
//   const { rows }: any = await db.execute(`SELECT * FROM ${tableName}`)
//   console.log(rows)
// }
