import { InlineKeyboard } from "grammy"

/**
 * Settings Keyboard
 * Creates keyboards for settings and configuration screens
 */
export class SettingsKeyboard {
  /**
   * Create settings keyboard
   */
  static create(): InlineKeyboard {
    return new InlineKeyboard()
      .text("🌐 Language", "settings:language")
      .text("🔔 Notifications", "settings:notifications")
      .row()
      .text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create back to settings keyboard
   */
  static createBackToSettings(): InlineKeyboard {
    return new InlineKeyboard()
      .text("🔙 Back to Settings", "wallet_action:settings")
      .text("🏠 Main Menu", "back:main_menu")
  }
}
