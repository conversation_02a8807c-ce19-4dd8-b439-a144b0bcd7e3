import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "./baseHandler"
import { ChainUtils } from "./chainUtils"
import { User, Wallet } from "../models"
import { BlockchainWallet } from "../blockchain/wallet"
import { BlockchainConfig } from "../blockchain/config"
import { log } from "../utils/log"

/**
 * Wallet import module
 * Handles wallet import functionality including chain selection, name/key input processing, and wallet import logic
 */
export class WalletImporter extends BaseHandler {
  /**
   * Show import wallet form - directly show network selection
   * @param ctx Callback query context
   */
  async showImportWalletForm(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()

    // Add chain selection buttons dynamically
    ChainUtils.addChainButtons(keyboard, "import_chain")
    keyboard.row().text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "importwallet_usage", keyboard)
  }

  /**
   * Handle import chain selection - directly prompt for wallet name
   * @param ctx Callback query context
   * @param params Chain parameters
   */
  async handleImportChain(ctx: any, params: string[]): Promise<void> {
    const chain = params[0] as string
    const userId = ctx.from?.id as number

    // Set up session to wait for wallet name input with selected chain
    await this.sessionSet(userId, {
      state: "waiting_import_details",
      operation: "import_wallet",
      chain: chain,
      step: "wallet_name"
    })

    const { symbol, chainDisplayName } = BlockchainConfig.get[chain]
    const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
    await this.updateKeyboard(ctx, "importwallet_name_prompt", keyboard, {
      chainName: chainDisplayName,
      symbol
    })
  }

  /**
   * Process private key input from user
   * @param ctx Message context
   * @param userId Telegram user ID
   * @param privateKey User input for private key
   * @param session Current session data
   */
  async processPrivateKeyInput(ctx: any, userId: number, privateKey: string, session: Record<string, any>): Promise<void> {
    try {
      // Validate private key format (basic validation)
      if (privateKey.length < 32) {
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "private_key_invalid", keyboard)
        return
      }

      // Process import with collected data
      const walletName = session.walletName
      const chain = session.chain
      const telegramId = ctx.from.id

      // Derive address from private key
      const address = await new BlockchainWallet(chain).getAddress(privateKey)
      if (!address) {
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "private_key_invalid", keyboard)
        return
      }

      // Get chain ID from blockchain config
      const { chainId, chainDisplayName } = BlockchainConfig.get[chain]

      const wallet = await Wallet.import(telegramId, walletName, chain, chainId, address, privateKey, this.passwordWallet)

      if (wallet && typeof wallet === "object") {
        // Success - wallet imported
        await this.sessionDelete(userId)

        const successKeyboard = new InlineKeyboard().text("👁️ View Wallet", `wallet_action:view:${wallet.id}`).text("📥 Import Another", "wallet_action:import").row().text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "importwallet_success", successKeyboard, {
          walletName: wallet.name,
          chainName: chainDisplayName,
          address: wallet.address
        })
      } else if (wallet === "DUPLICATE_NAME") {
        // Wallet name already exists
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
      } else if (wallet === "DUPLICATE_ADDRESS") {
        // Wallet address already exists for this chain
        const keyboard = new InlineKeyboard().text("💼 View My Wallets", "wallet_action:list").text("📥 Import Different Wallet", "wallet_action:import").row().text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "importwallet_duplicate_address", keyboard, {
          chainName: chainDisplayName,
          address: address
        })
      } else {
        // General import failure
        await this.sessionDelete(userId)

        const failureKeyboard = new InlineKeyboard().text("🔄 Try Again", "wallet_action:import").text("🏠 Back to Main Menu", "main_menu")
        await this.replyWithKeyboard(ctx, "importwallet_failed", failureKeyboard)
      }
    } catch (error) {
      log.error(`Error processPrivateKeyInput: ${error}`)
      await this.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "importwallet_error", errorKeyboard)
    }
  }

  /**
   * Process import details input from user (multi-step)
   * @param ctx Message context
   * @param userId Telegram user ID
   * @param userInput User input
   * @param session Current session data
   */
  async processImportDetailsInput(ctx: any, userId: number, userInput: string, session: Record<string, any>): Promise<void> {
    try {
      const step = session.step || "wallet_name"

      if (step === "wallet_name") {
        // Validate wallet name
        if (userInput.length > 32) {
          const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
          await this.replyWithKeyboard(ctx, "wallet_name_too_long", keyboard)
          return
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(userInput)) {
          const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
          await this.replyWithKeyboard(ctx, "wallet_name_invalid_chars", keyboard)
          return
        }

        // Check if wallet name already exists
        const user = await User.getById(userId)
        if (!user) {
          await this.sessionDelete(userId)
          const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
          await this.replyWithKeyboard(ctx, "user_not_found", keyboard)
          return
        }

        const existingWallet = await Wallet.getByName(user.id, userInput)
        if (existingWallet) {
          const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
          await this.replyWithKeyboard(ctx, "wallet_name_exists", keyboard)
          return
        }

        // Update session with wallet name and move to private key step
        await this.sessionSet(userId, {
          ...session,
          walletName: userInput,
          step: "private_key"
        })

        const { symbol, chainDisplayName } = BlockchainConfig.get[session.chain]
        const keyboard = new InlineKeyboard().text("🔙 Back to Networks", "wallet_action:import").text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "importwallet_private_key_prompt", keyboard, {
          chainName: chainDisplayName,
          symbol,
          walletName: userInput
        })
      } else if (step === "private_key") {
        // Process private key input
        await this.processPrivateKeyInput(ctx, userId, userInput, session)
      } else {
        log.warn(`Unknown import step: ${step}`)
        await this.sessionDelete(userId)
        await this.showImportWalletForm(ctx)
      }
    } catch (error) {
      log.error(`Error processImportDetailsInput: ${error}`)
      await this.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "importwallet_error", errorKeyboard)
    }
  }
}
