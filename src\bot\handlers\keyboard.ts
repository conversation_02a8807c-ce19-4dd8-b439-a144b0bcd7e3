import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "../baseHandler"
import { log } from "../../utils/log"

/**
 * Keyboard Handler
 * Provides keyboard utility methods for safe keyboard operations
 */
export class KeyboardHandler extends BaseHandler {
  /**
   * Update keyboard with error handling
   * @param ctx Context object
   * @param contentKey Content key
   * @param keyboard New keyboard
   * @param data Optional data for content replacement
   */
  async updateKeyboardSafe(ctx: any, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>): Promise<void> {
    try {
      await this.updateKeyboard(ctx, contentKey, keyboard, data)
    } catch (error) {
      log.error(`Error updating keyboard: ${error}`)
      await this.replyWithKeyboard(ctx, contentKey, keyboard, data)
    }
  }

  /**
   * Reply with keyboard and error handling
   * @param ctx Context object
   * @param contentKey Content key
   * @param keyboard Keyboard
   * @param data Optional data for content replacement
   */
  async replyWithKeyboardSafe(ctx: any, contentKey: string, keyboard: InlineKeyboard, data?: Record<string, any>): Promise<void> {
    try {
      await this.replyWithKeyboard(ctx, contentKey, keyboard, data)
    } catch (error) {
      log.error(`Error replying with keyboard: ${error}`)
      await this.reply(ctx, contentKey, data)
    }
  }
}
