import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "./baseHandler"

// Type definitions for module dependencies
interface ModuleDependencies {
  walletManager?: {
    showWalletList: (ctx: any) => Promise<void>
    showWalletListPage: (ctx: any, page: number) => Promise<void>
  }
  settingsManager?: {
    showHelpMenu: (ctx: any) => Promise<void>
  }
}

/**
 * Navigation management module
 * Handles navigation functionality including back navigation, pagination, and menu management
 */
export class NavigationManager extends BaseHandler {
  /**
   * Handle cancel callbacks
   * @param ctx Callback query context
   */
  async handleCancel(ctx: any): Promise<void> {
    const userId = ctx.from?.id as number

    // Clear any active session
    await this.sessionDelete(userId)

    await this.answerCallback(ctx, "callback_cancelled")
    await this.deleteKeyboard(ctx)

    // Show main menu after cancellation
    await this.showMainMenu(ctx)
  }

  /**
   * Handle back navigation callbacks
   * @param ctx Callback query context
   * @param params Navigation parameters
   * @param modules Module dependencies for navigation
   */
  async handleBack(ctx: any, params: string[], modules?: ModuleDependencies): Promise<void> {
    const destination = params[0]

    switch (destination) {
      case "main_menu":
        await this.showMainMenu(ctx)
        break
      case "wallets":
        if (modules?.walletManager) {
          await modules.walletManager.showWalletList(ctx)
        } else {
          await this.deleteKeyboard(ctx)
        }
        break
      case "help":
        if (modules?.settingsManager) {
          await modules.settingsManager.showHelpMenu(ctx)
        } else {
          await this.deleteKeyboard(ctx)
        }
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle pagination callbacks
   * @param ctx Callback query context
   * @param params Pagination parameters
   * @param modules Module dependencies for pagination
   */
  async handlePagination(ctx: any, params: string[], modules?: ModuleDependencies): Promise<void> {
    const type = params[0]
    const page = parseInt(params[1] as any)

    if (isNaN(page)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (type) {
      case "wallets":
        if (modules?.walletManager) {
          await modules.walletManager.showWalletListPage(ctx, page)
        } else {
          await this.deleteKeyboard(ctx)
        }
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  async showMainMenu(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("💼 My Wallets", "wallet_action:list").text("📊 View Stats", "wallet_action:stats").row().text("⚙️ Settings", "wallet_action:settings").text("❓ Help", "wallet_action:help")

    await this.updateKeyboard(ctx, "start", keyboard)
  }
}
