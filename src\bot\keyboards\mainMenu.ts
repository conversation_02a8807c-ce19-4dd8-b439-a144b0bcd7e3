import { InlineKeyboard } from "grammy"

/**
 * Main Menu Keyboard
 * Creates the main navigation menu keyboard
 */
export class MainMenuKeyboard {
  /**
   * Create main menu keyboard
   */
  static create(): InlineKeyboard {
    return new InlineKeyboard()
      .text("💼 My Wallets", "wallet_action:list")
      .text("📊 View Stats", "wallet_action:stats")
      .row()
      .text("⚙️ Settings", "wallet_action:settings")
      .text("❓ Help", "wallet_action:help")
  }
}
