import { <PERSON><PERSON><PERSON><PERSON> } from "../baseHandler"
import { SettingsKeyboard } from "../keyboards/settings"

/**
 * Settings Main Page
 * Handles main settings menu display
 */
export class SettingsMainPage extends BaseHandler {
  /**
   * Show settings menu
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = SettingsKeyboard.create()
    await this.updateKeyboard(ctx, "settings_menu", keyboard)
  }

  /**
   * Show language settings page
   * @param ctx Callback query context
   */
  async showLanguage(ctx: any): Promise<void> {
    const keyboard = SettingsKeyboard.createBackToSettings()
    await this.updateKeyboard(ctx, "settings_language", keyboard)
  }

  /**
   * Show notification settings page
   * @param ctx Callback query context
   */
  async showNotifications(ctx: any): Promise<void> {
    const keyboard = SettingsKeyboard.createBackToSettings()
    await this.updateKeyboard(ctx, "settings_notifications", keyboard)
  }
}
