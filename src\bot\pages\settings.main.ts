import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON>and<PERSON> } from "../baseHandler"

/**
 * Settings Main Page
 * Handles main settings menu display
 */
export class SettingsMainPage extends BaseHandler {
  /**
   * Create settings keyboard
   */
  private static createKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🌐 Language", "settings:language").text("🔔 Notifications", "settings:notifications").row().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create back to settings keyboard
   */
  private static createBackToSettingsKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Settings", "wallet_action:settings").text("🏠 Main Menu", "back:main_menu")
  }
  /**
   * Show settings menu
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = SettingsMainPage.createKeyboard()
    await this.updateKeyboard(ctx, "settings_menu", keyboard)
  }

  /**
   * Show language settings page
   * @param ctx Callback query context
   */
  async showLanguage(ctx: any): Promise<void> {
    const keyboard = SettingsMainPage.createBackToSettingsKeyboard()
    await this.updateKeyboard(ctx, "settings_language", keyboard)
  }

  /**
   * Show notification settings page
   * @param ctx Callback query context
   */
  async showNotifications(ctx: any): Promise<void> {
    const keyboard = SettingsMainPage.createBackToSettingsKeyboard()
    await this.updateKeyboard(ctx, "settings_notifications", keyboard)
  }
}
