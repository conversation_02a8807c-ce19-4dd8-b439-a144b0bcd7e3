import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON><PERSON><PERSON> } from "../baseHandler"

/**
 * Settings Main Page
 * Handles main settings menu display
 */
export class SettingsMainPage extends BaseHandler {
  /**
   * Show settings menu
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()
      .text("🌐 Language", "settings:language")
      .text("🔔 Notifications", "settings:notifications")
      .row()
      .text("🔙 Back to Menu", "back:main_menu")

    await this.updateKeyboard(ctx, "settings_menu", keyboard)
  }

  /**
   * Show language settings page
   * @param ctx Callback query context
   */
  async showLanguage(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()
      .text("🔙 Back to Settings", "wallet_action:settings")
      .text("🏠 Main Menu", "back:main_menu")
    
    await this.updateKeyboard(ctx, "settings_language", keyboard)
  }

  /**
   * Show notification settings page
   * @param ctx Callback query context
   */
  async showNotifications(ctx: any): Promise<void> {
    const keyboard = new InlineKeyboard()
      .text("🔙 Back to Settings", "wallet_action:settings")
      .text("🏠 Main Menu", "back:main_menu")
    
    await this.updateKeyboard(ctx, "settings_notifications", keyboard)
  }
}
