# Modular Telegram Bot Architecture

This directory contains the refactored, modular version of the Telegram trading bot. The code has been extracted from `main.ts` into specialized modules for better maintainability, testability, and organization.

## Architecture Overview

The modular architecture follows the principle of separation of concerns, with each module handling a specific aspect of the bot's functionality:

```
src/bot/
├── baseHandler.ts          # Core bot functionality and utilities
├── telegramBot.ts          # Main integration class
├── walletManager.ts        # Wallet operations and management
├── walletCreator.ts        # Wallet creation functionality
├── walletImporter.ts       # Wallet import functionality
├── statisticsManager.ts    # Analytics and statistics
├── settingsManager.ts      # Settings and help system
├── navigationManager.ts    # Navigation and menu management
├── index.ts               # Module exports
├── example.ts             # Usage examples
└── README.md              # This documentation
```

## Module Descriptions

### BaseHandler (`baseHandler.ts`)
- **Purpose**: Core bot functionality shared across all modules
- **Features**: Session management, keyboard utilities, message handling
- **Key Methods**: `sessionSet()`, `sessionGet()`, `replyWithKeyboard()`, `updateKeyboard()`

### TelegramBot (`telegramBot.ts`)
- **Purpose**: Main integration class that coordinates all modules
- **Features**: Bot initialization, callback routing, message handling
- **Key Methods**: `handleCallbackQuery()`, `handleTextMessage()`, `handleSessionInput()`

### WalletManager (`walletManager.ts`)
- **Purpose**: Wallet operations and management
- **Features**: Wallet listing, selection, details, balance, history, statistics
- **Key Methods**: `showWalletList()`, `handleWalletSelect()`, `showWalletBalance()`

### WalletCreator (`walletCreator.ts`)
- **Purpose**: Wallet creation functionality
- **Features**: Chain selection, name input processing, wallet generation
- **Key Methods**: `showCreateWalletForm()`, `processWalletNameInput()`

### WalletImporter (`walletImporter.ts`)
- **Purpose**: Wallet import functionality
- **Features**: Chain selection, name/key input processing, wallet import logic
- **Key Methods**: `showImportWalletForm()`, `processImportDetailsInput()`

### StatisticsManager (`statisticsManager.ts`)
- **Purpose**: Analytics and statistics functionality
- **Features**: User stats, wallet stats, chain stats, trading analytics
- **Key Methods**: `showUserStats()`, `handleStatsDisplay()`

### SettingsManager (`settingsManager.ts`)
- **Purpose**: Settings and help system
- **Features**: Settings management, help sections, chain information
- **Key Methods**: `showSettings()`, `showHelpMenu()`, `handleChainSelect()`

### NavigationManager (`navigationManager.ts`)
- **Purpose**: Navigation and menu management
- **Features**: Back navigation, pagination, main menu
- **Key Methods**: `showMainMenu()`, `handleBack()`, `handlePagination()`

## Usage

### Using the Modular Bot

To use the new modular architecture instead of the original `main.ts`:

1. **Import the modular bot**:
   ```typescript
   import { TelegramBot } from "./bot"
   ```

2. **Instantiate the bot**:
   ```typescript
   new TelegramBot()
   ```

### Backward Compatibility

The modular bot maintains full backward compatibility with the original implementation:
- All existing functionality is preserved
- API signatures remain unchanged
- Content system integration is maintained
- Session management works identically

### Direct Module Access

You can also import and use individual modules directly:

```typescript
import { WalletManager, StatisticsManager } from "./bot"

const walletManager = new WalletManager()
const statsManager = new StatisticsManager()
```

## Benefits of Modular Architecture

### 1. **Separation of Concerns**
- Each module has a single, well-defined responsibility
- Easier to understand and maintain individual components
- Reduced coupling between different functionalities

### 2. **Improved Maintainability**
- Changes to one module don't affect others
- Easier to locate and fix bugs
- Simpler to add new features

### 3. **Better Testability**
- Individual modules can be tested in isolation
- Easier to mock dependencies
- More focused unit tests

### 4. **Enhanced Reusability**
- Modules can be reused in different contexts
- Easier to extract common functionality
- Better code organization

### 5. **Scalability**
- Easy to add new modules for new features
- Simple to extend existing modules
- Better support for team development

## Migration Guide

### From Original main.ts

The original `main.ts` file remains unchanged and functional. To migrate to the modular architecture:

1. **Update your entry point** (e.g., `src/index.ts`):
   ```typescript
   // Before
   import { Telegram } from "./main"
   new Telegram()
   
   // After
   import { TelegramBot } from "./bot"
   new TelegramBot()
   ```

2. **No other changes required** - the modular bot is a drop-in replacement

### Adding New Features

To add new functionality:

1. **Create a new module** following the existing pattern
2. **Extend BaseHandler** for shared functionality
3. **Add integration** in `telegramBot.ts`
4. **Update exports** in `index.ts`

## Development Guidelines

### Module Structure
- Extend `BaseHandler` for shared functionality
- Use descriptive method names
- Include comprehensive JSDoc comments
- Follow existing error handling patterns

### Naming Conventions
- Use camelCase for file names
- Use PascalCase for class names
- Use descriptive method names
- Follow existing patterns

### Error Handling
- Use try-catch blocks for async operations
- Log errors with appropriate context
- Provide user-friendly error messages
- Maintain session state consistency

## Future Enhancements

The modular architecture enables easy implementation of:
- Additional blockchain support
- Advanced trading features
- Enhanced analytics
- User preference management
- Multi-language support
- Plugin system

## Conclusion

The modular architecture provides a solid foundation for the Telegram trading bot while maintaining full backward compatibility. It enables better development practices, easier maintenance, and enhanced scalability for future growth.
