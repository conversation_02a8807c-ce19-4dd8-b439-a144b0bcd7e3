⚙️ <b>Configuration & Settings Help</b>

Learn how to configure the bot and manage system settings using the interactive interface:

<b>⚙️ Accessing Settings:</b>
• Use <b>Settings</b> from the main menu to access all configuration options
• Navigate through different setting categories using buttons
• All changes are applied immediately

<b>💰 Fee Management:</b>
• <b>Fee Settings</b> - Configure trading fees and preferences
• View current fee structures by network
• Monitor fee receivers and percentage calculations
• Understand fee calculation methods

<b>🔗 Network Configuration:</b>
• <b>Chain Settings</b> - Manage blockchain network preferences
• View supported network details and chain IDs
• Check network-specific configurations
• Monitor network status and availability

<b>🌐 Language & Localization:</b>
• <b>Language Settings</b> - Set your preferred language
• Currently supports English with more languages coming
• Configure regional formatting preferences
• Set date/time display formats

<b>🔔 Notification Settings:</b>
• <b>Notification Preferences</b> - Configure alert settings
• Set up trading notifications
• Manage system alerts and updates
• Customize notification frequency

<b>🔗 Supported Networks:</b>
• <b>Solana</b> - Production Solana network (Chain ID: 101)
• <b>Ethereum</b> - Production Ethereum network (Chain ID: 1)

<b>💡 Configuration Best Practices:</b>
• Review fee settings before trading
• Understand network differences and costs
• Monitor fee changes regularly
• Use appropriate networks for your trading needs

<b>📱 Navigation Tips:</b>
• Use category buttons to access specific settings
• Back buttons return to previous menus
• Settings are saved automatically
• Changes take effect immediately

<b>🔍 Available Configuration Areas:</b>
• Trading fee percentages and calculations
• Network-specific settings and preferences
• User interface language and formatting
• Notification preferences and alerts
• Security settings and preferences

<b>⚠️ Important Notes:</b>
• Some settings may affect trading costs
• Network changes may require wallet recreation
• Language changes apply to all bot messages
• Notification settings affect alert frequency

Use the buttons below to navigate back:
