import { InlineKeyboard } from "grammy"
import { BaseHand<PERSON> } from "../baseHandler"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Chain Information Page
 * Handles blockchain chain information display
 */
export class ChainInfoPage extends BaseHandler {
  /**
   * Create chain info keyboard
   * @param chain Chain name
   */
  private static createChainInfoKeyboard(chain: string): InlineKeyboard {
    return new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("📊 Network Stats", `chain_detail:stats:${chain}`).row().text("🔙 Back to Menu", "back:main_menu")
  }
  /**
   * Show chain information
   * @param ctx Callback query context
   * @param chain Chain name
   */
  async show(ctx: any, chain: string): Promise<void> {
    const { chainId, symbol, chainDisplayName } = BlockchainConfig.get[chain]

    const keyboard = ChainInfoPage.createChainInfoKeyboard(chain)

    await this.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: chainDisplayName,
      symbol,
      chainId,
      chain
    })
  }

  /**
   * Show chain fees information
   * @param ctx Callback query context
   * @param chain Chain name
   */
  async showFees(ctx: any, chain: string): Promise<void> {
    const { chainId, symbol, chainDisplayName } = BlockchainConfig.get[chain]

    const keyboard = new InlineKeyboard().text("🔙 Back to Chain", `chain_select:${chain}:info`)

    // For now, show basic chain info as fees
    await this.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: chainDisplayName,
      symbol,
      chainId,
      chain
    })
  }

  /**
   * Show chain statistics
   * @param ctx Callback query context
   * @param chain Chain name
   */
  async showStats(ctx: any, chain: string): Promise<void> {
    const { chainId, symbol, chainDisplayName } = BlockchainConfig.get[chain]

    const keyboard = new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("🔙 Back to Chain", `chain_select:${chain}:info`)

    // For now, show basic chain info as stats
    await this.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: chainDisplayName,
      symbol,
      chainId,
      chain
    })
  }
}
