import { <PERSON><PERSON><PERSON><PERSON> } from "../baseHandler"
import { WalletListKeyboard } from "../keyboards/walletList"
import { User, Wallet } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Wallet List Page
 * Handles wallet listing with pagination
 */
export class WalletListPage extends BaseHandler {
  /**
   * Show wallet list with pagination
   * @param ctx Callback query context
   * @param page Page number (0-based, default: 0)
   */
  async show(ctx: any, page: number = 0): Promise<void> {
    try {
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.answerCallback(ctx, "callback_user_not_found")
        await this.deleteKeyboard(ctx)
        return
      }

      const wallets = await Wallet.getAllForOwner(user.id)

      if (wallets.length === 0) {
        await this.showEmptyState(ctx)
        return
      }

      await this.showWalletList(ctx, wallets, page)
    } catch (error) {
      log.error(`Error WalletListPage.show: ${error}`)
      await this.deleteKeyboard(ctx)
      await this.answerCallback(ctx, "callback_error_general")
    }
  }

  /**
   * Show empty wallet state
   * @param ctx Callback query context
   */
  private async showEmptyState(ctx: any): Promise<void> {
    const keyboard = WalletListKeyboard.createEmpty()
    await this.updateKeyboard(ctx, "wallets_empty", keyboard)
  }

  /**
   * Show wallet list with pagination
   * @param ctx Callback query context
   * @param wallets Array of wallets
   * @param page Current page number
   */
  private async showWalletList(ctx: any, wallets: any[], page: number): Promise<void> {
    // Add wallet buttons (max 5 per page for better UX)
    const walletsPerPage = 5
    const totalPages = Math.ceil(wallets.length / walletsPerPage)
    const currentPage = Math.max(0, Math.min(page, totalPages - 1))
    const startIndex = currentPage * walletsPerPage
    const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

    // Get wallets for current page with balance info
    const pageWallets = wallets.slice(startIndex, endIndex).map((wallet: any) => {
      const balance = wallet.balance.toString()
      const { symbol } = BlockchainConfig.get[wallet.chain]
      return {
        ...wallet,
        displayName: `${wallet.name} (${balance} ${symbol})`
      }
    })

    const keyboard = WalletListKeyboard.create(pageWallets, currentPage, totalPages)

    // Format wallet list for display
    const walletList = wallets
      .slice(startIndex, endIndex)
      .map((wallet, index) => {
        const balance = wallet.balance.toString()
        const { symbol } = BlockchainConfig.get[wallet.chain]
        return `${startIndex + index + 1}. ${wallet.name}\n   🔗 ${wallet.chain}\n   💰 ${balance} ${symbol}\n   📍 ${wallet.address}`
      })
      .join("\n\n")

    await this.updateKeyboard(ctx, "wallets_list", keyboard, {
      walletList,
      totalWallets: wallets.length,
      currentPage: currentPage + 1,
      totalPages
    })
  }
}
