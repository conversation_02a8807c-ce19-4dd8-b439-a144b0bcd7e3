/**
 * Blockchain network configuration state
 */
export const BlockchainConfig = {
  ethereum_mainnet: {
    chainId: 1,
    name: "Ethereum Mainnet",
    symbol: "ETH",
    rpcEndpoint: "https://mainnet.infura.io/v3/",
    explorerUrl: "https://etherscan.io",
    decimals: 18
  },
  ethereum_testnet: {
    chainId: 11155111, // Sepolia testnet
    name: "Ethereum Testnet",
    symbol: "ETH-TEST",
    rpcEndpoint: "https://sepolia.infura.io/v3/",
    explorerUrl: "https://sepolia.etherscan.io",
    decimals: 18
  },
  solana_mainnet: {
    chainId: 101,
    name: "Solana Mainnet",
    symbol: "SOL",
    rpcEndpoint: "https://api.mainnet-beta.solana.com",
    explorerUrl: "https://explorer.solana.com",
    decimals: 9
  },
  solana_devnet: {
    chainId: 103,
    name: "Solana Devnet",
    symbol: "SOL-DEV",
    rpcEndpoint: "https://api.devnet.solana.com",
    explorerUrl: "https://explorer.solana.com/?cluster=devnet",
    decimals: 9
  }
}
