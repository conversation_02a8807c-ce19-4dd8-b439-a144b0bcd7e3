/**
 * Blockchain network configuration state
 */
export class BlockchainConfig {
  static get: any = {
    ethereum: {
      chainId: 1,
      symbol: "ETH",
      rpcEndpoint: "https://mainnet.infura.io/v3/",
      explorerUrl: "https://etherscan.io",
      chainDisplayName: `Ethereum`,
      decimals: 18
    },
    solana: {
      chainId: 101,
      symbol: "SOL",
      rpcEndpoint: "https://api.mainnet-beta.solana.com",
      explorerUrl: "https://explorer.solana.com",
      chainDisplayName: `Solana`,
      decimals: 9
    }
  }
  static listName = Object.keys(this.get)
  static listChainId = this.listName.map((it) => this.get[it as keyof typeof this.get].chainId)
}
