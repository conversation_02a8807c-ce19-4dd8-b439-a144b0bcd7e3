/**
 * Bot module index file
 *
 * This file exports the modular Telegram bot architecture.
 * The new modular structure provides better maintainability and separation of concerns.
 */

// Export the new modular bot
export { TelegramBot } from "./telegramBot"

// Export individual modules for direct access if needed
export { BaseHandler } from "./baseHandler"
export { WalletManager } from "./walletManager"
export { WalletCreator } from "./walletCreator"
export { WalletImporter } from "./walletImporter"
export { StatisticsManager } from "./statisticsManager"
export { SettingsManager } from "./settingsManager"
export { NavigationManager } from "./navigationManager"
export { ChainUtils } from "./chainUtils"

// Re-export the original Telegram class for backward compatibility
export { Telegram } from "../main"
