/**
 * Bot module index file
 *
 * This file exports the page-based Telegram bot architecture.
 * The new page-based structure provides better maintainability and separation of concerns.
 */

// Export the new page-based bot
export { TelegramBot } from "./telegramBot"

// Export core utilities
export { BaseHandler } from "./baseHandler"
export { ChainUtils } from "./chainUtils"

// Export page router and individual pages
export * from "./pages"

// Export legacy modules for backward compatibility (deprecated)
export { WalletManager } from "./walletManager"
export { WalletCreator } from "./walletCreator"
export { WalletImporter } from "./walletImporter"
export { StatisticsManager } from "./statisticsManager"
export { SettingsManager } from "./settingsManager"
export { NavigationManager } from "./navigationManager"

// Re-export the original Telegram class for backward compatibility
export { Telegram } from "../main"
