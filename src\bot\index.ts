import { Bo<PERSON>, type CommandContext, Context, InlineKeyboard } from "grammy"
import { log } from "../utils/log"
import { User } from "../models"
import { BaseHandler } from "./baseHandler"
import { WalletManager } from "./walletManager"
import { WalletCreator } from "./walletCreator"
import { WalletImporter } from "./walletImporter"
import { StatisticsManager } from "./statisticsManager"
import { SettingsManager } from "./settingsManager"
import { NavigationManager } from "./navigationManager"

/**
 * Modular Telegram bot class
 * Integrates all specialized modules while maintaining existing API and functionality
 */
export class TelegramBot extends BaseHandler {
  private bot = new Bot(process.env.TELEGRAM_TOKEN as string)

  // Module instances
  private walletManager = new WalletManager()
  private walletCreator = new WalletCreator()
  private walletImporter = new WalletImporter()
  private statisticsManager = new StatisticsManager()
  private settingsManager = new SettingsManager()
  private navigationManager = new NavigationManager()

  constructor() {
    super()

    // Register callback query handlers for inline keyboards
    this.bot.on("callback_query:data", this.handleCallbackQuery.bind(this))

    // Handle any text message to show main menu (entry point for new users)
    this.bot.on("message:text", this.handleTextMessage.bind(this))

    // Handled error
    this.bot.catch((err) => {
      log.error(JSON.stringify(err))
    })

    // Started bot
    this.bot.start({
      onStart(botInfo) {
        log.info(`Telegram bot started: ${botInfo.username}`)
      }
    })
  }

  /**
   * Handle callback queries from inline keyboards
   * @param ctx Callback query context
   */
  private async handleCallbackQuery(ctx: any): Promise<void> {
    try {
      await ctx.answerCallbackQuery()

      const data = ctx.callbackQuery.data
      const [action, ...params] = data.split(":")

      switch (action) {
        case "wallet_action":
          await this.handleWalletAction(ctx, params)
          break
        case "wallet_select":
          await this.walletManager.handleWalletSelect(ctx, params)
          break
        case "wallet_detail":
          await this.walletManager.handleWalletDetail(ctx, params)
          break
        case "wallet_delete_select":
          await this.walletManager.handleWalletDeleteSelect(ctx, params)
          break
        case "chain_select":
          await this.settingsManager.handleChainSelect(ctx, params)
          break
        case "chain_detail":
          await this.statisticsManager.handleChainDetail(ctx, params)
          break
        case "confirm_delete":
          await this.walletManager.handleConfirmDelete(ctx, params)
          break
        case "export_confirm":
          await this.walletManager.handleExportConfirm(ctx, params)
          break
        case "create_wallet":
          await this.walletCreator.handleCreateWalletChain(ctx, params)
          break
        case "import_chain":
          await this.walletImporter.handleImportChain(ctx, params)
          break
        case "help":
          await this.settingsManager.handleHelpSection(ctx, params)
          break
        case "settings":
          await this.settingsManager.handleSettingsAction(ctx, params)
          break
        case "cancel":
          await this.navigationManager.handleCancel(ctx)
          break
        case "back":
          await this.handleBack(ctx, params)
          break
        case "page":
          await this.handlePagination(ctx, params)
          break
        case "noop":
          // No-operation: completely non-interactive button
          // Do nothing - no acknowledgment, no response, no UI changes
          break
        case "main_menu":
          await this.navigationManager.showMainMenu(ctx)
          break
        default:
          log.warn(`Unknown callback action: ${action}`)
          await this.deleteKeyboard(ctx)
      }
    } catch (error) {
      log.error(`Error handleCallbackQuery: ${error}`)
      await this.answerCallback(ctx, "callback_error_general")
      await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle text messages - check for active sessions or show main menu
   * @param ctx Message context from Grammy
   */
  private async handleTextMessage(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        const keyboard = new InlineKeyboard().text("🔄 Try Again", "main_menu")
        await this.replyWithKeyboard(ctx, "user_creation_failed", keyboard)
        return
      }

      // Check if user has an active session
      const hasSession = await this.sessionHas(telegramId)
      if (hasSession) {
        await this.handleSessionInput(ctx, telegramId)
        return
      }

      // No active session - show main menu
      await this.navigationManager.showMainMenu(ctx)
    } catch (error) {
      log.error(`Error handleTextMessage: ${error}`)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "general_error", keyboard)
    }
  }

  /**
   * Handle user text input based on active session state
   * @param ctx Message context from Grammy
   * @param userId Telegram user ID
   */
  private async handleSessionInput(ctx: any, userId: number): Promise<void> {
    try {
      const session = await this.sessionGet(userId)
      if (!session || !session.state) {
        await this.sessionDelete(userId)
        await this.navigationManager.showMainMenu(ctx)
        return
      }

      const userInput = ctx.message?.text?.trim()
      if (!userInput) {
        const keyboard = new InlineKeyboard().text("❌ Cancel", "cancel")
        await this.replyWithKeyboard(ctx, "input_invalid_empty", keyboard)
        return
      }

      switch (session.state) {
        case "waiting_wallet_name":
          await this.walletCreator.processWalletNameInput(ctx, userId, userInput, session)
          break
        case "waiting_private_key":
          await this.walletImporter.processPrivateKeyInput(ctx, userId, userInput, session)
          break
        case "waiting_import_details":
          await this.walletImporter.processImportDetailsInput(ctx, userId, userInput, session)
          break
        default:
          log.warn(`Unknown session state: ${session.state}`)
          await this.sessionDelete(userId)
          await this.navigationManager.showMainMenu(ctx)
      }
    } catch (error) {
      log.error(`Error handleSessionInput: ${error}`)
      await this.sessionDelete(userId)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await this.replyWithKeyboard(ctx, "session_error", keyboard)
    }
  }

  /**
   * Handle wallet action callbacks
   * @param ctx Callback query context
   * @param params Action parameters
   */
  private async handleWalletAction(ctx: any, params: string[]): Promise<void> {
    const action = params[0]
    switch (action) {
      case "list":
        await this.walletManager.showWalletList(ctx)
        break
      case "create":
        await this.walletCreator.showCreateWalletForm(ctx)
        break
      case "import":
        await this.walletImporter.showImportWalletForm(ctx)
        break
      case "stats":
        await this.statisticsManager.showUserStats(ctx)
        break
      case "settings":
        await this.settingsManager.showSettings(ctx)
        break
      case "help":
        await this.settingsManager.showHelpMenu(ctx)
        break
      case "view":
        await this.walletManager.handleWalletSelect(ctx, [params[1] as string])
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle back navigation callbacks
   * @param ctx Callback query context
   * @param params Navigation parameters
   */
  private async handleBack(ctx: any, params: string[]): Promise<void> {
    const destination = params[0]

    switch (destination) {
      case "main_menu":
        await this.navigationManager.showMainMenu(ctx)
        break
      case "wallets":
        await this.walletManager.showWalletList(ctx)
        break
      case "help":
        await this.settingsManager.showHelpMenu(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle pagination callbacks
   * @param ctx Callback query context
   * @param params Pagination parameters
   */
  private async handlePagination(ctx: any, params: string[]): Promise<void> {
    const type = params[0]
    const page = parseInt(params[1] as any)

    if (isNaN(page)) {
      await this.deleteKeyboard(ctx)
      return
    }

    switch (type) {
      case "wallets":
        await this.walletManager.showWalletListPage(ctx, page)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }
}
