import { <PERSON>Handler } from "../baseHandler"
import { User, Wallet } from "../../models"
import { log } from "../../utils/log"

// Import all page classes
import { MainMenuPage } from "./main.menu"
import { WalletListPage } from "./wallet.list"
import { WalletDetailsPage } from "./wallet.details"
import { WalletBalancePage } from "./wallet.balance"
import { WalletHistoryPage } from "./wallet.history"
import { WalletStatsPage } from "./wallet.stats"
import { WalletCreatePage } from "./wallet.create"
import { WalletImportPage } from "./wallet.import"
import { WalletExportPage } from "./wallet.export"
import { WalletDeletePage } from "./wallet.delete"
import { AccountStatsPage } from "./account.stats"
import { SettingsMainPage } from "./settings.main"
import { HelpSectionsPage } from "./help.sections"
import { ChainInfoPage } from "./chain.info"

/**
 * Page Router
 * Coordinates navigation between different pages and handles page-specific logic
 */
export class PageRouter extends BaseHandler {
  // Page instances
  private mainMenuPage = new MainMenuPage()
  private walletListPage = new WalletListPage()
  private walletDetailsPage = new WalletDetailsPage()
  private walletBalancePage = new WalletBalancePage()
  private walletHistoryPage = new WalletHistoryPage()
  private walletStatsPage = new WalletStatsPage()
  private walletCreatePage = new WalletCreatePage()
  private walletImportPage = new WalletImportPage()
  private walletExportPage = new WalletExportPage()
  private walletDeletePage = new WalletDeletePage()
  private accountStatsPage = new AccountStatsPage()
  private settingsMainPage = new SettingsMainPage()
  private helpSectionsPage = new HelpSectionsPage()
  private chainInfoPage = new ChainInfoPage()

  /**
   * Navigate to main menu
   */
  async showMainMenu(ctx: any): Promise<void> {
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Navigate to wallet list
   */
  async showWalletList(ctx: any, page: number = 0): Promise<void> {
    await this.walletListPage.show(ctx, page)
  }

  /**
   * Navigate to wallet details
   */
  async showWalletDetails(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    await this.walletDetailsPage.show(ctx, walletId, telegramId)
  }

  /**
   * Navigate to wallet balance page
   */
  async showWalletBalance(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }
    await this.walletBalancePage.show(ctx, wallet)
  }

  /**
   * Navigate to wallet history page
   */
  async showWalletHistory(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }
    await this.walletHistoryPage.show(ctx, wallet)
  }

  /**
   * Navigate to wallet stats page
   */
  async showWalletStats(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }
    await this.walletStatsPage.show(ctx, wallet)
  }

  /**
   * Navigate to wallet creation page
   */
  async showWalletCreate(ctx: any): Promise<void> {
    await this.walletCreatePage.show(ctx)
  }

  /**
   * Navigate to wallet creation name prompt
   */
  async showWalletCreateNamePrompt(ctx: any, chain: string): Promise<void> {
    const userId = ctx.from?.id as number
    await this.walletCreatePage.showNamePrompt(ctx, chain, userId)
  }

  /**
   * Process wallet creation
   */
  async processWalletCreation(ctx: any, userId: number, walletName: string, session: any): Promise<void> {
    await this.walletCreatePage.processCreation(ctx, userId, walletName, session.chain)
  }

  /**
   * Navigate to wallet import page
   */
  async showWalletImport(ctx: any): Promise<void> {
    await this.walletImportPage.show(ctx)
  }

  /**
   * Navigate to wallet import name prompt
   */
  async showWalletImportNamePrompt(ctx: any, chain: string): Promise<void> {
    const userId = ctx.from?.id as number
    await this.walletImportPage.showNamePrompt(ctx, chain, userId)
  }

  /**
   * Process wallet import details input
   */
  async processWalletImportDetails(ctx: any, userId: number, userInput: string, session: any): Promise<void> {
    const step = session.step || "wallet_name"

    if (step === "wallet_name") {
      // Validate wallet name
      if (userInput.length > 32) {
        await this.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
        return
      }

      if (!/^[a-zA-Z0-9_-]+$/.test(userInput)) {
        await this.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
        return
      }

      // Check if wallet name already exists
      const user = await User.getById(userId)
      if (!user) {
        await this.sessionDelete(userId)
        await this.showMainMenu(ctx)
        return
      }

      const existingWallet = await Wallet.getByName(user.id, userInput)
      if (existingWallet) {
        await this.walletImportPage.showFailure(ctx, session.chain, "duplicate_name")
        return
      }

      // Update session and show private key prompt
      await this.sessionSet(userId, {
        ...session,
        walletName: userInput,
        step: "private_key"
      })

      await this.walletImportPage.showPrivateKeyPrompt(ctx, session.chain, userInput, userId)
    } else if (step === "private_key") {
      // Process private key input
      await this.processWalletImportPrivateKey(ctx, userId, userInput, session)
    }
  }

  /**
   * Process wallet import private key
   */
  async processWalletImportPrivateKey(ctx: any, userId: number, privateKey: string, session: any): Promise<void> {
    // Validate private key format
    if (privateKey.length < 32) {
      await this.walletImportPage.showFailure(ctx, session.chain, "invalid_key")
      return
    }

    await this.walletImportPage.processImport(ctx, userId, session.walletName, privateKey, session.chain)
  }

  /**
   * Navigate to wallet export confirmation
   */
  async showWalletExportConfirmation(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }
    await this.walletExportPage.showConfirmation(ctx, wallet)
  }

  /**
   * Navigate to wallet export private key display
   */
  async showWalletExportPrivateKey(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }
    await this.walletExportPage.showPrivateKey(ctx, wallet, telegramId)
  }

  /**
   * Navigate to wallet delete confirmation
   */
  async showWalletDeleteConfirmation(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await this.answerCallback(ctx, "callback_wallet_access_denied")
      await this.deleteKeyboard(ctx)
      return
    }
    await this.walletDeletePage.showConfirmation(ctx, wallet)
  }

  /**
   * Process wallet deletion
   */
  async processWalletDelete(ctx: any, walletId: number): Promise<void> {
    const telegramId = ctx.from?.id as number
    await this.walletDeletePage.processDelete(ctx, walletId, telegramId)
  }

  /**
   * Navigate to account stats page
   */
  async showAccountStats(ctx: any): Promise<void> {
    await this.accountStatsPage.show(ctx)
  }

  /**
   * Navigate to settings main page
   */
  async showSettings(ctx: any): Promise<void> {
    await this.settingsMainPage.show(ctx)
  }

  /**
   * Navigate to settings language page
   */
  async showSettingsLanguage(ctx: any): Promise<void> {
    await this.settingsMainPage.showLanguage(ctx)
  }

  /**
   * Navigate to settings notifications page
   */
  async showSettingsNotifications(ctx: any): Promise<void> {
    await this.settingsMainPage.showNotifications(ctx)
  }

  /**
   * Navigate to help sections page
   */
  async showHelp(ctx: any): Promise<void> {
    await this.helpSectionsPage.show(ctx)
  }

  /**
   * Navigate to specific help section
   */
  async showHelpSection(ctx: any, section: string): Promise<void> {
    switch (section) {
      case "wallets":
        await this.helpSectionsPage.showWallets(ctx)
        break
      case "trading":
        await this.helpSectionsPage.showTrading(ctx)
        break
      case "config":
        await this.helpSectionsPage.showConfig(ctx)
        break
      default:
        await this.deleteKeyboard(ctx)
    }
  }

  /**
   * Navigate to chain info page
   */
  async showChainInfo(ctx: any, chain: string): Promise<void> {
    await this.chainInfoPage.show(ctx, chain)
  }

  /**
   * Navigate to chain fees page
   */
  async showChainFees(ctx: any, chain: string): Promise<void> {
    await this.chainInfoPage.showFees(ctx, chain)
  }

  /**
   * Navigate to chain stats page
   */
  async showChainStats(ctx: any, chain: string): Promise<void> {
    await this.chainInfoPage.showStats(ctx, chain)
  }
}
