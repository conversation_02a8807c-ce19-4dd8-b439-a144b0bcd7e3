import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON>and<PERSON> } from "../baseHandler"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Wallet Balance Page
 * Handles wallet balance display
 */
export class WalletBalancePage extends BaseHandler {
  /**
   * Show wallet balance details
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  async show(ctx: any, wallet: any): Promise<void> {
    const keyboard = new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${wallet.id}`)

    const balance = wallet.balance.toString()
    const { symbol } = BlockchainConfig.get[wallet.chain]
    
    await this.updateKeyboard(ctx, "balance_display", keyboard, {
      walletName: wallet.name,
      balance,
      symbol,
      chainName: wallet.chain
    })
  }
}
