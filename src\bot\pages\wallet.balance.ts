import { BaseHandler } from "../baseHandler"
import { WalletDetailsKeyboard } from "../keyboards/walletDetails"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Wallet Balance Page
 * Handles wallet balance display
 */
export class WalletBalancePage extends BaseHandler {
  /**
   * Show wallet balance details
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  async show(ctx: any, wallet: any): Promise<void> {
    const keyboard = WalletDetailsKeyboard.createBackToWallet(wallet.id)

    const balance = wallet.balance.toString()
    const { symbol } = BlockchainConfig.get[wallet.chain]

    await this.updateKeyboard(ctx, "balance_display", keyboard, {
      walletName: wallet.name,
      balance,
      symbol,
      chainName: wallet.chain
    })
  }
}
