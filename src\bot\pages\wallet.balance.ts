import { InlineKeyboard } from "grammy"
import { <PERSON>Hand<PERSON> } from "../baseHandler"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Wallet Balance Page
 * Handles wallet balance display
 */
export class WalletBalancePage extends BaseHandler {
  /**
   * Create back to wallet keyboard
   * @param walletId Wallet ID
   */
  private static createBackToWalletKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }
  /**
   * Show wallet balance details
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  async show(ctx: any, wallet: any): Promise<void> {
    const keyboard = WalletBalancePage.createBackToWalletKeyboard(wallet.id)

    const balance = wallet.balance.toString()
    const { symbol } = BlockchainConfig.get[wallet.chain]

    await this.updateKeyboard(ctx, "balance_display", keyboard, {
      walletName: wallet.name,
      balance,
      symbol,
      chainName: wallet.chain
    })
  }
}
