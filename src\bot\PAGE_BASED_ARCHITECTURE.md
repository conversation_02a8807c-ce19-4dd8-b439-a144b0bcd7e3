# 🏗️ Page-Based Architecture Documentation

## Overview

The Telegram bot has been refactored into a **production-level, page-based architecture** following NestJS routing patterns. This architecture organizes code by UI pages/screens rather than functional modules, providing better maintainability and clearer separation of concerns.

## 🎯 Architecture Principles

### **Page-Based Organization**
- Each file represents a specific UI page/screen
- Clear 1:1 mapping between files and user interfaces
- Follows NestJS naming conventions: `[feature].[page].ts`

### **Single Responsibility**
- Each page class handles only one specific UI screen
- No cross-page logic mixing
- Clear boundaries between different user interfaces

### **Production Standards**
- TypeScript strict typing
- Comprehensive error handling
- Consistent method signatures
- Proper dependency injection

## 📁 File Structure

```
src/bot/pages/
├── index.ts                 # Page exports
├── pageRouter.ts           # Central page coordinator
├── main.menu.ts            # Main menu page
├── wallet.list.ts          # Wallet listing page
├── wallet.details.ts       # Individual wallet details
├── wallet.balance.ts       # Wallet balance display
├── wallet.history.ts       # Wallet trading history
├── wallet.stats.ts         # Wallet statistics
├── wallet.create.ts        # Wallet creation flow
├── wallet.import.ts        # Wallet import flow
├── wallet.export.ts        # Wallet export functionality
├── wallet.delete.ts        # Wallet deletion
├── account.stats.ts        # Account-wide statistics
├── settings.main.ts        # Settings menu
├── help.sections.ts        # Help sections
└── chain.info.ts           # Blockchain information
```

## 🔧 Page Classes

### **Base Structure**
All page classes extend `BaseHandler` and follow this pattern:

```typescript
export class [Feature][Page]Page extends BaseHandler {
  /**
   * Main show method - displays the page
   * @param ctx Callback query context
   * @param ...params Page-specific parameters
   */
  async show(ctx: any, ...params: any[]): Promise<void> {
    // Page rendering logic
  }

  /**
   * Additional page-specific methods
   */
  private async helper(ctx: any): Promise<void> {
    // Helper methods for page functionality
  }
}
```

### **Page Responsibilities**

#### **Main Menu (`main.menu.ts`)**
- Displays the main navigation menu
- Entry point for all bot interactions

#### **Wallet Pages**
- **`wallet.list.ts`**: Wallet listing with pagination
- **`wallet.details.ts`**: Individual wallet information and actions
- **`wallet.balance.ts`**: Balance display for specific wallet
- **`wallet.history.ts`**: Trading history for specific wallet
- **`wallet.stats.ts`**: Statistics for specific wallet
- **`wallet.create.ts`**: Wallet creation flow and chain selection
- **`wallet.import.ts`**: Wallet import flow and validation
- **`wallet.export.ts`**: Private key export with security warnings
- **`wallet.delete.ts`**: Wallet deletion confirmation and processing

#### **Account Pages**
- **`account.stats.ts`**: Comprehensive account-wide analytics

#### **Settings & Help Pages**
- **`settings.main.ts`**: Settings menu and configuration
- **`help.sections.ts`**: Help documentation sections

#### **Chain Pages**
- **`chain.info.ts`**: Blockchain information and statistics

## 🎮 Page Router

The `PageRouter` class coordinates navigation between pages:

```typescript
export class PageRouter extends BaseHandler {
  // Page instances
  private mainMenuPage = new MainMenuPage()
  private walletListPage = new WalletListPage()
  // ... other page instances

  // Navigation methods
  async showMainMenu(ctx: any): Promise<void>
  async showWalletList(ctx: any, page?: number): Promise<void>
  async showWalletDetails(ctx: any, walletId: number): Promise<void>
  // ... other navigation methods
}
```

## 🚀 Usage Examples

### **Basic Page Navigation**

```typescript
import { TelegramBot } from "./bot"

// Initialize the page-based bot
const bot = new TelegramBot()

// The bot automatically handles page routing based on user interactions
```

### **Direct Page Access**

```typescript
import { WalletListPage, WalletDetailsPage } from "./bot/pages"

// Create page instances
const walletListPage = new WalletListPage()
const walletDetailsPage = new WalletDetailsPage()

// Use pages directly
await walletListPage.show(ctx)
await walletDetailsPage.show(ctx, walletId, telegramId)
```

### **Custom Page Router**

```typescript
import { PageRouter } from "./bot/pages"

// Create custom router
const pageRouter = new PageRouter()

// Navigate to specific pages
await pageRouter.showMainMenu(ctx)
await pageRouter.showWalletList(ctx, 0)
await pageRouter.showWalletDetails(ctx, 123)
```

## 🔄 Migration from Module-Based

### **Before (Module-Based)**
```typescript
// Old approach - functional modules
import { WalletManager, StatisticsManager } from "./bot"

const walletManager = new WalletManager()
await walletManager.showWalletList(ctx)
await walletManager.showWalletBalance(ctx, wallet)
```

### **After (Page-Based)**
```typescript
// New approach - page-based
import { PageRouter } from "./bot/pages"

const pageRouter = new PageRouter()
await pageRouter.showWalletList(ctx)
await pageRouter.showWalletBalance(ctx, walletId)
```

## ✅ Benefits

### **1. Clear UI Mapping**
- Direct correspondence between files and user interfaces
- Easy to locate code for specific screens
- Intuitive file organization

### **2. Better Maintainability**
- Changes to one page don't affect others
- Clear boundaries and responsibilities
- Easier debugging and testing

### **3. Scalability**
- Easy to add new pages
- Simple to modify existing pages
- Clear extension patterns

### **4. Developer Experience**
- Intuitive file naming
- Consistent patterns across pages
- Easy onboarding for new developers

### **5. Production Ready**
- Comprehensive error handling
- TypeScript strict typing
- Performance optimized
- Memory efficient

## 🔧 Development Guidelines

### **Adding New Pages**

1. **Create page file**: `src/bot/pages/[feature].[page].ts`
2. **Extend BaseHandler**: `export class [Feature][Page]Page extends BaseHandler`
3. **Implement show method**: Main page display logic
4. **Add to PageRouter**: Navigation method in `pageRouter.ts`
5. **Export in index**: Add to `src/bot/pages/index.ts`

### **Page Method Patterns**

```typescript
// Main display method (required)
async show(ctx: any, ...params: any[]): Promise<void>

// Success/failure states
async showSuccess(ctx: any, data: any): Promise<void>
async showFailure(ctx: any, error: string): Promise<void>

// Confirmation dialogs
async showConfirmation(ctx: any, data: any): Promise<void>

// Processing methods
async process[Action](ctx: any, ...params: any[]): Promise<void>
```

### **Error Handling**

```typescript
async show(ctx: any): Promise<void> {
  try {
    // Page logic
  } catch (error) {
    log.error(`Error [PageName].show: ${error}`)
    await this.answerCallback(ctx, "callback_error_general")
    await this.deleteKeyboard(ctx)
  }
}
```

## 🎉 Conclusion

The page-based architecture provides a clean, maintainable, and scalable foundation for the Telegram bot. It follows industry best practices and provides excellent developer experience while maintaining production-level quality and performance.
