import { InlineKeyboard } from "grammy"

/**
 * Wallet Details Keyboard
 * Creates keyboards for wallet detail actions and navigation
 */
export class WalletDetailsKeyboard {
  /**
   * Create wallet details keyboard
   * @param walletId Wallet ID
   */
  static create(walletId: number): InlineKeyboard {
    return new InlineKeyboard()
      .text("💰 Balance", `wallet_detail:balance:${walletId}`)
      .text("📊 History", `wallet_detail:history:${walletId}`)
      .row()
      .text("📈 Stats", `wallet_detail:stats:${walletId}`)
      .text("🔑 Export Key", `wallet_detail:export:${walletId}`)
      .row()
      .text("🗑️ Delete", `wallet_detail:delete:${walletId}`)
      .text("🔙 Back to Wallets", "wallet_action:list")
  }

  /**
   * Create back to wallet keyboard
   * @param walletId Wallet ID
   */
  static createBackToWallet(walletId: number): InlineKeyboard {
    return new InlineKeyboard()
      .text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }

  /**
   * Create export confirmation keyboard
   * @param walletId Wallet ID
   */
  static createExportConfirmation(walletId: number): InlineKeyboard {
    return new InlineKeyboard()
      .text("🔑 Yes, Export Key", `export_confirm:${walletId}`)
      .text("🔙 Cancel", `wallet_select:${walletId}`)
  }

  /**
   * Create delete confirmation keyboard
   * @param walletId Wallet ID
   */
  static createDeleteConfirmation(walletId: number): InlineKeyboard {
    return new InlineKeyboard()
      .text("✅ Yes, Delete", `confirm_delete:${walletId}`)
      .text("❌ Cancel", "cancel")
      .row()
      .text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }
}
