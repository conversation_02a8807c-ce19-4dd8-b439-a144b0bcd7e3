{"name": "client-13-1-trading-bot", "version": "0.0.1", "module": "index.ts", "type": "module", "private": true, "scripts": {"start": "bun ./src/index.ts", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate"}, "devDependencies": {"@types/bun": "latest", "drizzle-kit": "^0.31.1"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@electric-sql/pglite": "^0.3.4", "@grammyjs/conversations": "^2.1.0", "@solana/addresses": "^2.1.1", "@solana/web3.js": "^1.98.2", "bs58": "^6.0.0", "chokidar": "^4.0.3", "drizzle-orm": "^0.43.1", "ethers": "^6.14.4", "grammy": "^1.36.1", "keyv": "^5.3.4", "logging-pretty": "^3.0.0", "micromustache": "^8.0.3", "postgres": "^3.4.7", "safe-my-text": "^2.0.0"}}