/**
 * Trading history schema definition
 *
 * This file defines the trading_history table schema.
 * Separated from wallets table for better performance and scalability.
 */

import { sql } from "drizzle-orm"
import { bigint, boolean, index, pgTable, serial, timestamp, varchar, text } from "drizzle-orm/pg-core"
import { tableWallets } from "./wallets"

export const tradingHistory = pgTable(
  "trading_history",
  {
    id: serial().primaryKey(), // Auto-incrementing primary key
    walletId: bigint({ mode: "bigint" })
      .notNull()
      .references(() => tableWallets.id), // Foreign key to wallets table
    chain: text().notNull(), // Network type
    success: boolean().notNull().default(true), // Trade success status
    amount: bigint({ mode: "bigint" }).notNull(), // Trade amount
    operation: varchar({ length: 1 }).notNull(), // Trade operation (buy/sell)
    closedAt: timestamp().default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => new Date()),
    createdAt: timestamp().default(sql`CURRENT_TIMESTAMP`)
  },
  (table) => [
    // Add index on walletId for faster queries
    index("trading_history_wallet_id_idx").on(table.walletId),
    // Add index on network for faster filtering
    index("trading_history_network_idx").on(table.chain)
  ]
)
