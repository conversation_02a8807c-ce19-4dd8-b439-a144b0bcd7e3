🔔 <b>Notification Settings</b>

Configure your notification preferences for trading activities\.

<b>Notification Types:</b>
• <b>Trade Confirmations</b> - Get notified when trades complete
• <b>Balance Updates</b> - Receive balance change notifications
• <b>System Alerts</b> - Important system messages
• <b>Error Notifications</b> - Failed transaction alerts

<b>Current Settings:</b>
• All notifications are currently enabled
• Notifications are sent via Telegram messages
• Real-time updates for important events

<b>Notification Features:</b>
• Instant trade confirmations
• Balance change alerts
• System maintenance notices
• Security alerts
• Error and failure notifications

<b>Note:</b> 
Notification preferences will be customizable in future updates. Currently, all important notifications are enabled by default for security and transparency.

Use the buttons below to navigate back:
