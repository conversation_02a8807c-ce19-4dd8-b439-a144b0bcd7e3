import { InlineKeyboard } from "grammy"

/**
 * Common Keyboard
 * Creates common keyboard patterns used across the application
 */
export class CommonKeyboard {
  /**
   * Create confirmation keyboard
   * @param confirmAction Confirm action callback
   * @param cancelAction Cancel action callback
   * @param backAction Optional back action callback
   */
  static createConfirmation(
    confirmAction: string,
    cancelAction: string,
    backAction?: string
  ): InlineKeyboard {
    const keyboard = new InlineKeyboard()
      .text("✅ Yes", confirmAction)
      .text("❌ No", cancelAction)

    if (backAction) {
      keyboard.row().text("🔙 Back", backAction)
    }

    return keyboard
  }

  /**
   * Create input prompt keyboard
   * @param backAction Back action callback
   * @param cancelAction Cancel action callback
   */
  static createInputPrompt(backAction: string, cancelAction: string = "cancel"): InlineKeyboard {
    return new InlineKeyboard()
      .text("🔙 Back", backAction)
      .text("❌ Cancel", cancelAction)
  }

  /**
   * Create success keyboard
   * @param primaryAction Primary action callback and text
   * @param secondaryAction Secondary action callback and text
   * @param backToMenu Whether to include back to menu button
   */
  static createSuccess(
    primaryAction: { text: string; callback: string },
    secondaryAction: { text: string; callback: string },
    backToMenu: boolean = true
  ): InlineKeyboard {
    const keyboard = new InlineKeyboard()
      .text(primaryAction.text, primaryAction.callback)
      .text(secondaryAction.text, secondaryAction.callback)

    if (backToMenu) {
      keyboard.row().text("🏠 Back to Main Menu", "main_menu")
    }

    return keyboard
  }

  /**
   * Create failure keyboard
   * @param retryAction Retry action callback
   * @param backToMenu Whether to include back to menu button
   */
  static createFailure(retryAction: string, backToMenu: boolean = true): InlineKeyboard {
    const keyboard = new InlineKeyboard().text("🔄 Try Again", retryAction)

    if (backToMenu) {
      keyboard.text("🏠 Back to Main Menu", "main_menu")
    }

    return keyboard
  }
}
